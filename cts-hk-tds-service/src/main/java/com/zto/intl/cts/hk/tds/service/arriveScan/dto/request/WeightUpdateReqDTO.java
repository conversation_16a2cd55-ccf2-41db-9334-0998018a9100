package com.zto.intl.cts.hk.tds.service.arriveScan.dto.request;

import com.zto.intl.cts.hk.core.enums.ScanNoTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 重量更新请求DTO
 * <AUTHOR>
 * @date 2025/08/01
 */
@Data
public class WeightUpdateReqDTO {

    /**
     * 单号类型
     */
    private ScanNoTypeEnum noTypeEnum;

    /** 单号列表 **/
    private List<String> noList;

    /** 重量 单位kg **/
    private BigDecimal weight;

    /** 长 单位cm **/
    private BigDecimal length;

    /** 宽 单位cm **/
    private BigDecimal width;

    /** 高 单位cm **/
    private BigDecimal height;

    /** 操作人 **/
    private String operator;

    /** 操作人所属站点 **/
    private String siteCode;

    /** 操作人所属站点名称 **/
    private String siteName;
}
