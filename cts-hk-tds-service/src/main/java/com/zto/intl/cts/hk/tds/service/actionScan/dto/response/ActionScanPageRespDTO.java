package com.zto.intl.cts.hk.tds.service.actionScan.dto.response;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description 状态扫描分页查询
 * @Date 2025/8/1
 * @Version 1.0
 */
@Getter
@Setter
public class ActionScanPageRespDTO {

    /**
     * 操作时间
     */
    private Date operateTime;
    /**
     * 运单号
     */
    private String billCode;

    /**
     * 客户订单号
     */
    private String customerOrderNo;

    /**
     * 操作状态描述
     */
    private String actionName;

    /**
     * 是否执行成功
     */
    private Boolean isSuccess;

    /**
     * 执行失败原因
     */
    private String failMessage;

    /**
     * 操作人
     */
    private String operator;
}
