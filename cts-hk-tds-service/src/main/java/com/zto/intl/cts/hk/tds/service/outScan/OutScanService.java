package com.zto.intl.cts.hk.tds.service.outScan;

import com.zto.intl.cts.hk.core.domain.web.PageQuery;
import com.zto.intl.cts.hk.core.domain.web.PageResult;
import com.zto.intl.cts.hk.core.enums.ScanNoTypeEnum;
import com.zto.intl.cts.hk.tds.service.outScan.dto.request.OutScanQueryReqDTO;
import com.zto.intl.cts.hk.tds.service.outScan.dto.request.OutScanReqDTO;
import com.zto.intl.cts.hk.tds.service.outScan.dto.response.OutOrderRespDTO;
import com.zto.intl.cts.hk.tds.service.outScan.dto.response.OutScanCheckRespDTO;
import com.zto.intl.cts.hk.tds.service.outScan.dto.response.OutScanQueryRespDTO;
import com.zto.intl.cts.hk.tds.service.outScan.dto.response.OutScanRespDTO;

import java.util.List;
import java.util.Map;

/**
 * 出货扫描服务
 * <AUTHOR>
 * @date 20225/7/24
 */
public interface OutScanService {

    /**
     * 出货扫描
     */
    OutScanRespDTO outScan(OutScanReqDTO outScanDTO, Map<String, OutOrderRespDTO> omsOrderMap);

    /**
     * 查询出货扫描结果
     */
    PageResult<OutScanQueryRespDTO> queryOutScanResult(PageQuery<OutScanQueryReqDTO> pageQuery);

    /**
     * 校验单号
     */
    OutScanCheckRespDTO checkBillCodes(List<String> noList, ScanNoTypeEnum noTypeEnum, Map<String, OutOrderRespDTO> orderInfoMap);

}
