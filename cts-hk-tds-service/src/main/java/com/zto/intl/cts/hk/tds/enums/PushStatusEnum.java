package com.zto.intl.cts.hk.tds.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum PushStatusEnum {

    NOT_PUSH((byte)0, "不推送"),
    WAIT_PUSH((byte)1, "待推送"),
    PUSH_SUCCESS((byte)2, "推送成功"),
    PUSH_FAIL((byte)3, "推送失败"),
    PUSH_SUSPEND((byte)4, "挂起不再自动重试"),
    ;

    private final Byte code;
    private final String text;

    public static final Map<Byte, PushStatusEnum> codeMap = new HashMap<>();

    static {
        for (PushStatusEnum value : values()) {
            codeMap.put(value.code, value);
        }
    }

    public static PushStatusEnum getByCode(Byte code) {
        return codeMap.get(code);
    }
}
