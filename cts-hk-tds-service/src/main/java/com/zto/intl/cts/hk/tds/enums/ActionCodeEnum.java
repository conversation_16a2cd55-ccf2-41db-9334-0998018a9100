package com.zto.intl.cts.hk.tds.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum ActionCodeEnum {
    C10("10", "到件"),
    C30("30", "发货"),
    ;

    private final String code;
    private final String text;

    public static final Map<String, ActionCodeEnum> codeMap = new HashMap<>();

    static {
        for (ActionCodeEnum value : values()) {
            codeMap.put(value.code, value);
        }
    }

    /**
     * 通过code获取OmsOrderStatusEnum
     */
    public static ActionCodeEnum getByCode(String code) {
        return codeMap.get(code);
    }

    /**
     * 通过code获取text
     */
    public static String getText(String code) {
        ActionCodeEnum omsOrderStatusEnum = codeMap.get(code);
        if(omsOrderStatusEnum != null) {
            return omsOrderStatusEnum.getText();
        }

        return code;
    }
}
