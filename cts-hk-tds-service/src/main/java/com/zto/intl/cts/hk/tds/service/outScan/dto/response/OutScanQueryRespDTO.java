package com.zto.intl.cts.hk.tds.service.outScan.dto.response;

import lombok.Data;
import java.util.Date;

/**
 * 出货扫描查询结果
 * <AUTHOR>
 * @date 2025/07/24
 */
@Data
public class OutScanQueryRespDTO {

    /** 序列 **/
    private Integer seq;

    /** 扫描时间 **/
    private Date operateTime;

    /** 运单号 **/
    private String billCode;

    /** 客户单号 **/
    private String customerOrderNo;

    /** 扫描结果 **/
    private Boolean isSuccess;

    /** 扫描结果描述 **/
    private String scanResultDesc;

    /** 扫描失败原因 **/
    private String failMessage;

    /** 揽收人 **/
    private String operator;
}
