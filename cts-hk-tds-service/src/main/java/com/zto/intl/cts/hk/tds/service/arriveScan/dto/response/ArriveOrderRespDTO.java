package com.zto.intl.cts.hk.tds.service.arriveScan.dto.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/07/25
 */
@Data
public class ArriveOrderRespDTO {

    /** 运单号 **/
    private String billCode;

    /** 客户单号 **/
    private String customerOrderNo;

    /** 支付状态 **/
    private Byte payStatus;

    /** 支付状态描述 **/
    private String payStatusDesc;

    /** 收件人名称 **/
    private String consigneeName;

    /** 收件人电话 **/
    private String consigneeMobile;

    /** 收件人详细地址 **/
    private String consigneeAddress;

    /** 末端派送方式**/
    private String deliveryMethod;

    /** 末端派送方式描述 **/
    private String deliveryMethodDesc;

    /** 商品总件数 **/
    private Integer billCount;

    /**  运单状态 **/
    private String billStatus;

    /** 订单来源 **/
    private String dataSource;

    /** 订单结算重 **/
    private BigDecimal chargeWeight;

    /** 仓库重 **/
    private BigDecimal warehouseWeight;

    /** 预报重 **/
    private BigDecimal forecastWeight;

}
