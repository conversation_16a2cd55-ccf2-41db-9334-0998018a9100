package com.zto.intl.cts.hk.tds.service.arriveScan;

import com.zto.intl.cts.hk.core.domain.web.PageQuery;
import com.zto.intl.cts.hk.core.domain.web.PageResult;
import com.zto.intl.cts.hk.tds.dao.entity.ArriveScan;
import com.zto.intl.cts.hk.tds.service.arriveScan.dto.request.ArriveScanQueryReqDTO;
import com.zto.intl.cts.hk.tds.service.arriveScan.dto.request.ArriveScanReqDTO;
import com.zto.intl.cts.hk.tds.service.arriveScan.dto.request.WeightUpdateReqDTO;
import com.zto.intl.cts.hk.core.enums.ScanNoTypeEnum;
import com.zto.intl.cts.hk.tds.service.arriveScan.dto.response.ArriveOrderRespDTO;
import com.zto.intl.cts.hk.tds.service.arriveScan.dto.response.ArriveScanCheckRespDTO;
import com.zto.intl.cts.hk.tds.service.arriveScan.dto.response.ArriveScanQueryRespDTO;
import com.zto.intl.cts.hk.tds.service.arriveScan.dto.response.ArriveScanRespDTO;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 到货扫描服务
 * <AUTHOR>
 * @date 20225/7/24
 */
public interface ArriveScanService {

    /**
     * 到货扫描
     */
    ArriveScanRespDTO arriveScan(ArriveScanReqDTO arriveScanDTO, Map<String, ArriveOrderRespDTO> omsOrderMap);

    /**
     * 更新推送结果和推送消息
     */
    void updatePushResultWithMessage(String billNo, Byte pushStatus, String pushResult);

    /**
     * 批量更新推送状态
     */
    void batchUpdatePushStatus(List<String> billNos, Byte pushStatus);

    /**
     * 更新重量和尺寸
     */
    void updateWeightAndSize(WeightUpdateReqDTO weightUpdateReqDTO);

    /**
     * 查询到货扫描结果
     */
    PageResult<ArriveScanQueryRespDTO> queryArriveScanResult(PageQuery<ArriveScanQueryReqDTO> pageQuery);

    /**
     * 校验单号（支持批量和重量差异校验）
     */
    ArriveScanCheckRespDTO checkBillCodes(List<String> noList, ScanNoTypeEnum noTypeEnum, BigDecimal weight, Map<String, ArriveOrderRespDTO> orderInfoMap);

    /**
     * 查询已存在的到货扫描记录
     */
    Map<String, ArriveScan> getExistingScanMap(List<String> noList, ScanNoTypeEnum noTypeEnum);

    /**
     * 查询待推送的扫描记录
     */
    List<ArriveScan> queryPendingPushRecords(int limit);

    /**
     * 更新推送次数和下次执行时间
     */
    void updatePushInfo(Long id, Integer pushCount, Date nextExecuteTime, Byte pushStatus, String pushResult);

    /**
     * 根据运单号更新支付状态
     */
    void updatePayStatusByBillCode(String billCode, Byte payStatus);

    /**
     * 查询已完成的到货扫描记录
     */
    List<ArriveScan> queryArriveScan(String billNo);
}
