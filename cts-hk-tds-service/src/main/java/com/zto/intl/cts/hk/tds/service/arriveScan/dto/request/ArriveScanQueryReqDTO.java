package com.zto.intl.cts.hk.tds.service.arriveScan.dto.request;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 20225/7/24
 */
@Data
public class ArriveScanQueryReqDTO {

    /**
     * 开始扫描时间
     */
    private Date startTime;

    /** 结束扫描时间 **/
    private Date endTime;

    /** 支付状态 **/
    private Byte payStatus;

    /** 扫描结果 1-成功 0-失败 **/
    private Boolean scanResult;

    /** 勾选导出 ids **/
    private List<Long> ids;
}
