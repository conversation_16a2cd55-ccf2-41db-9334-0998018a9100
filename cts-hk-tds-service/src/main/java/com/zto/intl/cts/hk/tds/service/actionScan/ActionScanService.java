package com.zto.intl.cts.hk.tds.service.actionScan;

import com.zto.intl.cts.hk.core.domain.web.PageQuery;
import com.zto.intl.cts.hk.core.domain.web.PageResult;
import com.zto.intl.cts.hk.tds.service.actionScan.dto.request.ActionScanPageReqDTO;
import com.zto.intl.cts.hk.tds.service.actionScan.dto.request.ActionScanReqDTO;
import com.zto.intl.cts.hk.tds.service.actionScan.dto.response.ActionScanPageRespDTO;
import com.zto.intl.cts.hk.tds.service.actionScan.dto.response.ActionScanRespDTO;

public interface ActionScanService {

    void actionScan(ActionScanReqDTO reqDTO);

    ActionScanRespDTO actionScanBatch(ActionScanReqDTO reqDTO);

    PageResult<ActionScanPageRespDTO> page(PageQuery<ActionScanPageReqDTO> query);

    Boolean isOverride(String billCode, String customerCode, String siteCode, String actionCode);
}
