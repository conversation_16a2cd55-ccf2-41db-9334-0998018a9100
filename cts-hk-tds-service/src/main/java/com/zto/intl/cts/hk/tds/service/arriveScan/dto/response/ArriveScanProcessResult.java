package com.zto.intl.cts.hk.tds.service.arriveScan.dto.response;

import com.zto.intl.cts.hk.core.domain.web.ErrorInfo;
import com.zto.intl.cts.hk.tds.dao.entity.ArriveScan;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/25
 */
@Data
public class ArriveScanProcessResult {
    /**
     * 扫描结果列表
     */
    private final List<ArriveScan> scanList;

    /**
     * 成功数量
     */
    private final int successCount;

    /**
     * 失败数量
     */
    private final int failCount;

    /**
     * 运单总数
     */
    private final int totalBillCount;

    /**
     * 扫描成功的运单号列表
     */
    private final List<ArriveOrderRespDTO> successNoList;

    /**
     * 错误信息列表
     */
    private final List<ErrorInfo> errors;

    public ArriveScanProcessResult(List<ArriveScan> scanList, List<ArriveOrderRespDTO> successNoList,
                                   int successCount, int failCount, int totalBillCount, List<ErrorInfo> errors) {
        this.scanList = scanList;
        this.successCount = successCount;
        this.failCount = failCount;
        this.totalBillCount = totalBillCount;
        this.successNoList = successNoList;
        this.errors = errors;
    }
}
