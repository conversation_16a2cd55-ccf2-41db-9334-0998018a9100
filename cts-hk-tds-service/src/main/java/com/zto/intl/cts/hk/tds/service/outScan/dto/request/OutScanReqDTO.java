package com.zto.intl.cts.hk.tds.service.outScan.dto.request;

import com.zto.intl.cts.hk.core.domain.web.ErrorInfo;
import com.zto.intl.cts.hk.core.enums.ScanNoTypeEnum;
import com.zto.intl.cts.hk.core.enums.ScanTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/24
 */
@Data
public class OutScanReqDTO {
    /**
     * 单号类型
     */
    private ScanNoTypeEnum noTypeEnum;

    /** 单号 **/
    private List<String> noList;

    /** 操作人 **/
    private String operator;

    /** 操作人所属站点 **/
    private String siteCode;

    /** 操作人所属站点名称 **/
    private String siteName;

    /** 扫描类型 1-单个扫描 2-批量扫描 **/
    private ScanTypeEnum scanType;

    private ErrorInfo errorInfo;

}
