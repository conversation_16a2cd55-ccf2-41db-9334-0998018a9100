package com.zto.intl.cts.hk.tds.service.task.trace.impl;

import com.zto.intl.cts.hk.commons.util.BeanUtil;
import com.zto.intl.cts.hk.tds.dao.entity.TraceTask;
import com.zto.intl.cts.hk.tds.dao.mapper.TraceTaskMapper;
import com.zto.intl.cts.hk.tds.service.task.trace.TraceTaskService;
import com.zto.intl.cts.hk.tds.service.task.trace.dto.request.TaskTraceAddReqDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 轨迹任务服务实现类
 *
 * <AUTHOR>
 * @date 2025/7/30
 * @version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TraceTaskServiceImpl implements TraceTaskService {

    // 默认配置常量
    private static final String DEFAULT_MOBILE = "666666";
    private static final String DEFAULT_COUNTRY_NAME = "HONGKONG";
    private static final String DEFAULT_COUNTRY_CODE = "HK";
    private static final int DEFAULT_PUSH_COUNT = 0;

    private final TraceTaskMapper traceTaskMapper;

    /**
     * 添加单个轨迹任务
     */
    @Override
    public void addTask(TaskTraceAddReqDTO reqDTO) {
        TraceTask traceTask = buildEnt(reqDTO);
        traceTaskMapper.insert(traceTask);
    }

    /**
     * 批量添加轨迹任务
     */
    @Override
    public void addTaskBatch(List<TaskTraceAddReqDTO> reqDTOs){
        List<TraceTask> tasks = reqDTOs.stream().map(this::buildEnt).collect(Collectors.toList());
        traceTaskMapper.insertList(tasks);
    }


    private TraceTask buildEnt(TaskTraceAddReqDTO reqDTO){
        TraceTask traceTask = BeanUtil.copyProperties(reqDTO, new TraceTask());
        traceTask.setMobile(DEFAULT_MOBILE);
        traceTask.setCountry(DEFAULT_COUNTRY_NAME);
        traceTask.setCountryCode(DEFAULT_COUNTRY_CODE);
        traceTask.setMessage(reqDTO.getActionName());
        traceTask.setNextExecuteTime(new Date());
        traceTask.setPushCount(DEFAULT_PUSH_COUNT);
        return traceTask;
    }
}
