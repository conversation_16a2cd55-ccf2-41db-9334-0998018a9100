package com.zto.intl.cts.hk.tds.service.actionScan.dto.request;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 状态扫描请求
 * @Date 2025/7/29
 * @Version 1.0
 */
@Getter
@Setter
public class ActionScanReqDTO implements Serializable {
    private static final long serialVersionUID = 2352599016345775935L;

    private String billCode;
    private List<String> billCodes;
    private String customerOrderNo;
    private List<String> customerOrderNos;
    private String actionCode;
    private String actionName;
    private String userCode;
    private String siteCode;
    private List<OrderInfo> orderInfos;
    private ActionMachineResult actionMachineFailInfo;


    @Getter
    @Setter
    public static class ActionMachineResult{

        private String errrorMsg;
        private List<String> billNos;

        public ActionMachineResult(String errrorMsg){
            this.errrorMsg = errrorMsg;
        }
        public ActionMachineResult(String errrorMsg, List<String> billNos){
            this.errrorMsg = errrorMsg;
            this.billNos = billNos;
        }
    }

    @Getter
    @Setter
    public static class OrderInfo{

        /** 运单号 **/
        private String billCode;

        /** 客户单号 **/
        private String customerOrderNo;

        /** 支付状态 **/
        private Byte payStatus;

        /** 末端派送方式**/
        private String deliveryMethod;

        /**  运单状态 **/
        private String billStatus;

        /** 订单来源 **/
        private String dataSource;
    }
}
