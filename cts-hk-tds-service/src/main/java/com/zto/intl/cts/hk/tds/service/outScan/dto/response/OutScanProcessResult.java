package com.zto.intl.cts.hk.tds.service.outScan.dto.response;

import com.zto.intl.cts.hk.core.domain.web.ErrorInfo;
import com.zto.intl.cts.hk.tds.dao.entity.ArriveScan;
import com.zto.intl.cts.hk.tds.dao.entity.OutScan;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/25
 */
@Data
public class OutScanProcessResult {
    /**
     * 扫描结果列表
     */
    private final List<OutScan> scanList;

    /**
     * 成功数量
     */
    private final int successCount;

    /**
     * 失败数量
     */
    private final int failCount;

    /**
     * 运单总数
     */
    private final int totalBillCount;

    /**
     * 扫描成功的运单号列表
     */
    private final List<OutOrderRespDTO> successNoList;

    /**
     * 错误信息列表
     */
    private final List<ErrorInfo> errors;

    public OutScanProcessResult(List<OutScan> scanList, List<OutOrderRespDTO> successNoList,
                                int successCount, int failCount, int totalBillCount, List<ErrorInfo> errors) {
        this.scanList = scanList;
        this.successCount = successCount;
        this.failCount = failCount;
        this.totalBillCount = totalBillCount;
        this.successNoList = successNoList;
        this.errors = errors;
    }
}
