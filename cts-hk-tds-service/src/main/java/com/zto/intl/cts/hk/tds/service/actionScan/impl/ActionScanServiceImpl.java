package com.zto.intl.cts.hk.tds.service.actionScan.impl;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.zto.intl.cts.hk.commons.util.Asserts;
import com.zto.intl.cts.hk.commons.util.BeanUtil;
import com.zto.intl.cts.hk.commons.util.PageMapUtil;
import com.zto.intl.cts.hk.commons.util.TransactionExecutor;
import com.zto.intl.cts.hk.core.domain.web.PageQuery;
import com.zto.intl.cts.hk.core.domain.web.PageResult;
import com.zto.intl.cts.hk.tds.dao.entity.ActionScan;
import com.zto.intl.cts.hk.tds.dao.mapper.ActionScanMapper;
import com.zto.intl.cts.hk.tds.error.ErrorEnum;
import com.zto.intl.cts.hk.tds.service.actionScan.ActionScanService;
import com.zto.intl.cts.hk.tds.service.actionScan.dto.request.ActionScanPageReqDTO;
import com.zto.intl.cts.hk.tds.service.actionScan.dto.request.ActionScanReqDTO;
import com.zto.intl.cts.hk.tds.service.actionScan.dto.response.ActionScanPageRespDTO;
import com.zto.intl.cts.hk.tds.service.actionScan.dto.response.ActionScanRespDTO;
import com.zto.intl.cts.hk.tds.service.task.trace.TraceTaskService;
import com.zto.intl.cts.hk.tds.service.task.trace.dto.request.TaskTraceAddReqDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 状态扫描实现
 * @Date 2025/7/29
 * @Version 1.0
 */
@Service
public class ActionScanServiceImpl implements ActionScanService {

    @Autowired
    private TraceTaskService traceTaskService;
    @Autowired
    private ActionScanMapper actionScanMapper;
    @Autowired
    private TransactionExecutor transactionExecutor;

    @Override
    public void actionScan(ActionScanReqDTO reqDTO){

        // 封装扫描记录
        ActionScan actionScan = buildScanEnt(reqDTO);

        // 封装轨迹信息
        TaskTraceAddReqDTO traceAddReqDTO = buildTraceEnt(actionScan);

        // 事务入库
        transactionExecutor.run(()->{
            traceTaskService.addTask(traceAddReqDTO);
            actionScanMapper.insert(actionScan);
        });

        // 抛出业务异常
        Asserts.isTrue(actionScan.getIsSuccess(), ErrorEnum.ActionScanError.ACTION_OVERRIDE);
    }

    @Override
    public ActionScanRespDTO actionScanBatch(ActionScanReqDTO reqDTO){

        Boolean isBillNoScan = CollectionUtils.isEmpty(reqDTO.getBillCodes()) ? false :true;

        // 单号不存在的订单-- 不需要保存扫描记录
        Set<String> orderNos = reqDTO.getOrderInfos().stream()
                .map(e -> isBillNoScan ? e.getCustomerOrderNo() : e.getBillCode())
                .collect(Collectors.toSet());

        List<String> scanNos = isBillNoScan ? reqDTO.getCustomerOrderNos() : reqDTO.getBillCodes();

        List<String> notExist = scanNos;
        if(orderNos.size() != scanNos.size()){
            notExist.removeAll(orderNos);
        }

        // 封装扫描记录
        List<ActionScan> scanList = reqDTO.getOrderInfos().stream()
                .map(e -> buildScanEnt(e, reqDTO)).collect(Collectors.toList());
        // 封装轨迹信息
        List<TaskTraceAddReqDTO> traceList = scanList.stream().map(e -> buildTraceEnt(e)).collect(Collectors.toList());

        // 构建失败信息
        List<ActionScan> fails = scanList.stream().filter(e -> !e.getIsSuccess()).collect(Collectors.toList());
        Map<String, List<String>> failsMessage = fails.stream()
                .collect(Collectors.groupingBy(ActionScan::getFailMessage,
                        Collectors.mapping(ActionScan::getBillCode, Collectors.toList())));
        failsMessage.put(ErrorEnum.ActionScanError.NO_EXIST.getMessage(), notExist);

        // 入库
        // 事务入库
        transactionExecutor.run(()-> {
            actionScanMapper.insertList(scanList);
            traceTaskService.addTaskBatch(traceList);
        });

        return new ActionScanRespDTO(scanList.size() - fails.size(), fails.size(), failsMessage);
    }

    @Override
    public PageResult<ActionScanPageRespDTO> page(PageQuery<ActionScanPageReqDTO> reqDTO) {

        Example example = new Example(ActionScan.class);
        Example.Criteria criteria = example.createCriteria();
        ActionScanPageReqDTO condition = reqDTO.getCondition();
        if(null != condition.getStartTime()) {
            criteria.andGreaterThanOrEqualTo(ActionScan.OPERATE_TIME, condition.getStartTime());
        }
        if(null != condition.getEndTime()) {
            criteria.andLessThanOrEqualTo(ActionScan.OPERATE_TIME, condition.getEndTime());
        }
        if(null != condition.getIsSuccess()) {
            criteria.andEqualTo(ActionScan.IS_SUCCESS, condition.getIsSuccess());
        }

        Page<ActionScan> page = PageHelper.startPage(reqDTO.getPageNum(), reqDTO.getPageSize(), true);
        actionScanMapper.selectByExample(example);

        return PageMapUtil.map(page, ActionScanPageRespDTO.class);

    }

    private ActionScan buildScanEnt(ActionScanReqDTO.OrderInfo orderInfo, ActionScanReqDTO reqDTO){
        return buildScanEnt(reqDTO, orderInfo, reqDTO.getActionMachineFailInfo());
    }
    private ActionScan buildScanEnt(ActionScanReqDTO reqDTO){
        return buildScanEnt(reqDTO, reqDTO.getOrderInfos().get(0), reqDTO.getActionMachineFailInfo());
    }
    private ActionScan buildScanEnt(ActionScanReqDTO reqDTO, ActionScanReqDTO.OrderInfo orderInfo, ActionScanReqDTO.ActionMachineResult actionMachineFailInfo){

        // 封装记录
        ActionScan actionScan = BeanUtil.copyProperties(reqDTO, new ActionScan());
        actionScan.setBillCode(orderInfo.getBillCode());
        actionScan.setCustomerOrderNo(orderInfo.getCustomerOrderNo());
        actionScan.setIsSuccess(true);

        if(null != actionMachineFailInfo){
            actionScan.setIsSuccess(false);
            actionScan.setFailMessage(actionMachineFailInfo.getErrrorMsg());
            return actionScan;
        }

        // 是否重复扫描
        if(isOverride(reqDTO.getBillCode(), reqDTO.getCustomerOrderNo(), reqDTO.getSiteCode(), reqDTO.getActionCode())){
            actionScan.setIsSuccess(false);
            actionScan.setFailMessage(ErrorEnum.ActionScanError.ACTION_OVERRIDE.getMessage());
        }
        return actionScan;
    }

    public Boolean isOverride(String billCode, String customerCode, String siteCode, String actionCode){
        Example example = new Example(ActionScan.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo(ActionScan.SITE_CODE, siteCode)
                .andEqualTo(ActionScan.ACTION_CODE, actionCode)
                .andEqualTo(ActionScan.IS_SUCCESS, true);
        if(StringUtil.isNotBlank(billCode)){
            criteria.andEqualTo(ActionScan.BILL_CODE, billCode);
        }
        if(StringUtil.isNotBlank(customerCode)){
            criteria.andEqualTo(ActionScan.CUSTOMER_ORDER_NO, customerCode);
        }

        List<ActionScan> dbActions = actionScanMapper.selectByExample(example);
        return !CollectionUtils.isEmpty(dbActions);
    }

    private TaskTraceAddReqDTO buildTraceEnt(ActionScan actionScan){

        TaskTraceAddReqDTO traceAddReqDTO = BeanUtil.copyProperties(actionScan, new TaskTraceAddReqDTO());
        return traceAddReqDTO;

    }
}
