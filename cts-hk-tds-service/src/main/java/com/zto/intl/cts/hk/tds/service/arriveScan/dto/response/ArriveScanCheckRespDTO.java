package com.zto.intl.cts.hk.tds.service.arriveScan.dto.response;

import com.zto.intl.cts.hk.core.domain.web.ErrorInfo;
import lombok.Data;
import java.util.List;

/**
 * 扫描校验结果DTO
 * 
 * <AUTHOR>
 * @date 2025/8/1
 */
@Data
public class ArriveScanCheckRespDTO {

    /**
     * 是否需要二次确认弹窗
     */
    private Boolean needConfirm = false;

    /**
     * 校验错误信息列表
     */
    private List<ErrorInfo> errors;

}
