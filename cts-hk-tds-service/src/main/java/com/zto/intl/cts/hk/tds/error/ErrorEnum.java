package com.zto.intl.cts.hk.tds.error;

import com.zto.intl.cts.hk.core.exception.IError;
import lombok.AllArgsConstructor;
import lombok.Getter;

public class ErrorEnum {

    private static final String ERROR_MSG_FORMAT = "%s";
    /**
     * 通用错误码
     * 500 ~ 1000
     */
    @Getter
    @AllArgsConstructor
    public enum CommonError implements IError {
        // 系统错误
        SYSTEM_ERROR(500, "system error"),
        // 配置错误
        CONFIG_ERROR(501, "config error"),
        // 参数错误
        PARAMETER_ERROR(502, "parameter error"),
        // 依赖服务异常
        DEPENDENT_API_INVOKE_FAIL(503, "dependent api invoke fail"),
        // 记录不存在
        RECORD_NOT_EXISTS(504, "记录不存在"),
        // 记录已存在
        RECORD_ALREADY_EXISTED(505, "record alread existed"),
        // 数据库更新失败
        DB_UPDATE_FAIL(506, "db record update fail"),
        // 数据库插入失败
        DB_INSERT_FAIL(507, "insert into db fail"),
        // 数据库删除失败
        DB_DELETE_FAIL(508, "delete from db fail"),
        //没有权限
        HAVE_NO_AUTH(509,"auth check error"),
        //线程池拒绝
        THREAD_POOL_REJECT(510,"thread pool reject"),
        //es查询失败
        ES_SEARCH_FAIL(511,"es search fail"),
        //基础资料服务调用失败
        BASE_SERVIC_INVOKE_FAIL(512,"base service invoke fail"),
        //json转化失败
        JSON_PARSE_FAIL(513,"message parse fail"),
        // 并发导致异常
        CONCURRENT_ERROR(514,"concurrent error"),
        // 批量处理超限
        BATCH_SIZE_LIMITED(515, "batch size limited"),
        // 数据库操作失败
        DB_OPERATE_FAIL(516, "db operate fail"),
        //批量操作失败
        BTACH_OPERATE_FAIL(517,"batch operate fail"),
        //redis操作失敗
        REDIS_OPERATE_FAIL(518,"redis operate fail"),
        GET_LOCK_EXCEPTION(520,"获取锁异常"),
        SEND_MSG_FAILED(521, "发送消息失败"),
        EXPORT_EXCEL_UPLOAD_FAIL(522,"导出excel上传失败"),
        EXPORT_EXCEL_SIGN_EXCEPTION(523,"导出excel签名异常"),
        GET_URL_FAIL(524,"获取文件下载地址失败"),
        PARAMETER_IS_EMPTY(525,"参数为空"),
        START_AFTER_END(526,"开始日期大于结束日期"),
        DATE_OVER_ONE_MONTH(527,"日期相差超过30天"),
        TRY_LATER(528, "稍后重试"),
        GET_OWNER_SITE_INFO_NULL(529,"根据所属站点获取信息为空"),
        DATA_QUERY_ERROR(530, "数据查询异常"),
        ;

        private final Integer code;
        private final String message;

    }


    /**
     * 到货扫描模块错误码
     * 1001 ~ 1100
     */
    @Getter
    @AllArgsConstructor
    public enum ArriveScanError implements IError {
        NO_EXIST(1002, "运单不存在"),
        REPEAT_SCAN(1003, "重复扫描！"),
        PLTS_REQUIRE_WEIGHT(1004, "香港普罗托斯运单必须称重"),
        SCAN_QTY_LIMIT(1005, "扫描订单数不能超过1000"),
        SCAN_NOT_ALLOW(1006, "运单状态错误，无法进行扫描"),
        WEIGHT_NOT_NULL(1007, "重量不能为空"),
        WEIGHT_DIFFERENCE_CHECK(1008, "重量差异超过 5KG"),
        ;
        private final Integer code;
        private final String message;
    }

    /**
     * 状态扫描模块错误码
     * 1101 ~ 1200
     */
    @Getter
    @AllArgsConstructor
    public enum ActionScanError implements IError {

        ACTION_OVERRIDE(1101,"不能在同一站点重复扫描"),
        BATCH_SCAN_FAIL(1102,"批量扫描失败"),
        NO_EXIST(1103, "单号不存在"),
        ;

        private final Integer code;
        private final String message;
    }

    /**
     * 出货扫描模块错误码
     * 1201 ~ 1300
     */
    @Getter
    @AllArgsConstructor
    public enum OutScanError implements IError {
        NO_EXIST(1201, "运单不存在"),
        REPEAT_SCAN(1202, "重复扫描！"),
        PLTS_NOT_PAYED(1203, "运单未支付完成"),
        SCAN_QTY_LIMIT(1204, "扫描订单数不能超过1000"),
        SCAN_NOT_ALLOW(1205, "运单状态错误，无法进行扫描"),
        ARRIVE_SCAN_NO_RECORD(1206, "未进行到货扫描"),
        ;
        private final Integer code;
        private final String message;
    }

//    /**
//     * 轨迹任务错误码
//     * 1301 ~ 1400
//     */
//    @Getter
//    @AllArgsConstructor
//    public enum TraceTaskError implements IError {
//
//        PUSH_FAIL(1301, "推送轨迹失败"),
//        ;
//        private final Integer code;
//        private final String message;
//    }

    /**
     * OMS交互模块错误码
     * 2000 ~ 2100
     */
    @Getter
    @AllArgsConstructor
    public enum OmsError implements IError {
        BILL_NO_NULL(2000, "运单号不能为空"),
        PAY_STATUS_NULL(2001, "支付状态不能为空"),
        ARRIVE_SCAN_RECORD_NULL(2002, "未完成到货扫描"),
        PAY_STATUS_UPDATE_ERROR(2003, "支付状态更新异常"),
        ;
        private final Integer code;
        private final String message;
    }
}
