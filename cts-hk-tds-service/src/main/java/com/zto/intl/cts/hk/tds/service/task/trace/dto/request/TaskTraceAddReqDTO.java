package com.zto.intl.cts.hk.tds.service.task.trace.dto.request;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 轨迹任务新增
 * @Date 2025/7/30
 * @Version 1.0
 */
@Data
public class TaskTraceAddReqDTO {

    /**
     * 运单号
     */
    private String billCode;

    /**
     * 客户订单号
     */
    private String customerOrderNo;

    /**
     * 操作状态码
     */
    private String actionCode;

    /**
     * 操作状态描述
     */
    private String actionName;

    /**
     * 轨迹状态码
     */
    private String traceAction;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    private Date operateTime;

    /**
     * 站点编码
     */
    private String siteCode;

    /**
     * 站点名称
     */
    private String siteName;


}
