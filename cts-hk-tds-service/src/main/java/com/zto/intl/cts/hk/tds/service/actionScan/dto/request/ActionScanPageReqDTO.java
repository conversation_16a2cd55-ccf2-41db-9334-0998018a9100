package com.zto.intl.cts.hk.tds.service.actionScan.dto.request;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 状态扫描分页查询请求
 * @Date 2025/8/1
 * @Version 1.0
 */
@Getter
@Setter
public class ActionScanPageReqDTO {

    /**
     * 扫描开始时间
     */
    private Date startTime;

    /**
     * 扫描结束时间
     */
    private Date endTime;

    /**
     * 是否执行成功
     */
    private Boolean isSuccess;
}
