package com.zto.intl.cts.hk.tds.service.actionScan.dto.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 批量扫描响应
 * @Date 2025/7/30
 * @Version 1.0
 */
@Data
public class ActionScanRespDTO implements Serializable {

    private static final long serialVersionUID = 1482036332903492043L;
    /**
     * 成功数
     */
    private Integer successCount;

    /**
     * 失败数
     */
    private Integer failCount;

    /**
     * 详细失败信息
     */
    private Map<String, List<String>> failMessage;

    public ActionScanRespDTO(Integer successCount, Integer failCount, Map<String, List<String>> failMessage) {
        this.successCount = successCount;
        this.failCount = failCount;
        this.failMessage = failMessage;
    }

    public Integer getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(Integer successCount) {
        this.successCount = successCount;
    }

    public Integer getFailCount() {
        return failCount;
    }

    public void setFailCount(Integer failCount) {
        this.failCount = failCount;
    }
}
