package com.zto.intl.cts.hk.base.dao.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@ToString
@Accessors(chain = true)
@Table(name = "`user_info`")
public class UserInfo implements Serializable {
    /**
     * 主键id
     */
    @Id
    @Column(name = "`id`")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 创建时间
     */
    @Column(name = "`gmt_create`")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @Column(name = "`gmt_modified`")
    private Date gmtModified;

    /**
     * 创建人
     */
    @Column(name = "`creator`")
    private String creator;

    /**
     * 修改人
     */
    @Column(name = "`modifier`")
    private String modifier;

    public static final String ID = "id";

    public static final String DB_ID = "id";

    public static final String GMT_CREATE = "gmtCreate";

    public static final String DB_GMT_CREATE = "gmt_create";

    public static final String GMT_MODIFIED = "gmtModified";

    public static final String DB_GMT_MODIFIED = "gmt_modified";

    public static final String CREATOR = "creator";

    public static final String DB_CREATOR = "creator";

    public static final String MODIFIER = "modifier";

    public static final String DB_MODIFIER = "modifier";

    private static final long serialVersionUID = 1L;
}