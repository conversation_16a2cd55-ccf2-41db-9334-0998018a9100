package com.zto.intl.cts.hk.base.dao.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@ToString
@Accessors(chain = true)
@Table(name = "`trace_action`")
public class TraceAction implements Serializable {
    /**
     * 主键id
     */
    @Id
    @Column(name = "`id`")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 操作状态码
     */
    @Column(name = "`action_code`")
    private String actionCode;

    /**
     * 操作状态描述
     */
    @Column(name = "`action_name`")
    private String actionName;

    /**
     * 轨迹状态码
     */
    @Column(name = "`trace_action_code`")
    private String traceActionCode;

    /**
     * 是否推送轨迹系统，0 否 1 是
     */
    @Column(name = "`need_push`")
    private Byte needPush;

    /**
     * 创建时间
     */
    @Column(name = "`gmt_create`")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @Column(name = "`gmt_modified`")
    private Date gmtModified;

    /**
     * 创建人
     */
    @Column(name = "`creator`")
    private String creator;

    /**
     * 修改人
     */
    @Column(name = "`modifier`")
    private String modifier;

    /**
     * 轨迹描述,例如：澳门仓入库
     */
    @Column(name = "`trace_description`")
    private String traceDescription;

    /**
     * 订单状态白名单
     */
    @Column(name = "`white_list`")
    private String whiteList;

    /**
     * 订单状态黑名单
     */
    @Column(name = "`black_list`")
    private String blackList;

    public static final String ID = "id";

    public static final String DB_ID = "id";

    public static final String ACTION_CODE = "actionCode";

    public static final String DB_ACTION_CODE = "action_code";

    public static final String ACTION_NAME = "actionName";

    public static final String DB_ACTION_NAME = "action_name";

    public static final String TRACE_ACTION_CODE = "traceActionCode";

    public static final String DB_TRACE_ACTION_CODE = "trace_action_code";

    public static final String NEED_PUSH = "needPush";

    public static final String DB_NEED_PUSH = "need_push";

    public static final String GMT_CREATE = "gmtCreate";

    public static final String DB_GMT_CREATE = "gmt_create";

    public static final String GMT_MODIFIED = "gmtModified";

    public static final String DB_GMT_MODIFIED = "gmt_modified";

    public static final String CREATOR = "creator";

    public static final String DB_CREATOR = "creator";

    public static final String MODIFIER = "modifier";

    public static final String DB_MODIFIER = "modifier";

    public static final String TRACE_DESCRIPTION = "traceDescription";

    public static final String DB_TRACE_DESCRIPTION = "trace_description";

    public static final String WHITE_LIST = "whiteList";

    public static final String DB_WHITE_LIST = "white_list";

    public static final String BLACK_LIST = "blackList";

    public static final String DB_BLACK_LIST = "black_list";

    private static final long serialVersionUID = 1L;
}