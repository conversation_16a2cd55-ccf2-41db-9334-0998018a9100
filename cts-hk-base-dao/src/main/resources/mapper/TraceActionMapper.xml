<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.intl.cts.hk.base.dao.mapper.TraceActionMapper">
  <resultMap id="BaseResultMap" type="com.zto.intl.cts.hk.base.dao.entity.TraceAction">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="action_code" jdbcType="VARCHAR" property="actionCode" />
    <result column="action_name" jdbcType="VARCHAR" property="actionName" />
    <result column="trace_action_code" jdbcType="VARCHAR" property="traceActionCode" />
    <result column="need_push" jdbcType="TINYINT" property="needPush" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="trace_description" jdbcType="VARCHAR" property="traceDescription" />
    <result column="white_list" jdbcType="VARCHAR" property="whiteList" />
    <result column="black_list" jdbcType="VARCHAR" property="blackList" />
  </resultMap>
</mapper>