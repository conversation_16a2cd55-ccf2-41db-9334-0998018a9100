<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>

    <properties resource="application-dao-fat.properties"/>

    <context id="mysql" targetRuntime="MyBatis3Simple">

        <!-- 1. 可选: 属性配置 -->
        <property name="javaFileEncoding" value="UTF-8"/>


        <!-- 使用通用Mapper插件添加JPA注解 -->
        <plugin type="tk.mybatis.mapper.generator.MapperPlugin">
            <property name="mappers" value="com.zto.intl.cts.hk.base.dao.BaseMapper"/>
            <property name="caseSensitive" value="true"/>
            <property name="persistence" value="true"/> <!-- 关键：生成JPA注解 -->
            <property name="beginningDelimiter" value="`"/>
            <property name="endingDelimiter" value="`"/>
            <property name="generateColumnConsts" value="true"/>
            <property name="lombok" value="Getter,Setter,ToString,Accessors"/>
        </plugin>
        <!-- 2. 可选: 插件 -->
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>

        <!-- 注释生成器配置 -->
        <commentGenerator>
            <!-- 不抑制所有注释 -->
            <property name="suppressAllComments" value="false"/>
            <!-- 不抑制日期 -->
            <property name="suppressDate" value="true"/>
            <!-- 添加表/列备注作为注释 -->
            <property name="addRemarkComments" value="true"/>
        </commentGenerator>

        <!-- 4. 必需: 数据库连接 (二选一) -->
        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="${titans.mybatis.config.datasource.hikari.jdbcUrl}"
                        userId="${titans.mybatis.config.datasource.hikari.username}"
                        password="${titans.mybatis.config.datasource.hikari.password}">
        </jdbcConnection>

        <!-- 5. 可选: Java类型解析器 -->
        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <!-- 6. 必需: Java模型生成器 -->
        <javaModelGenerator targetPackage="com.zto.intl.cts.hk.base.dao.entity"
                            targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaModelGenerator>

        <!-- 7. 必需: SQL映射文件生成器 -->
        <sqlMapGenerator targetPackage="mapper"
                         targetProject="src/main/resources">
        </sqlMapGenerator>

        <!-- 8. 可选: Java客户端生成器 -->
        <javaClientGenerator type="XMLMAPPER"
                             targetPackage="com.zto.intl.cts.hk.base.dao.mapper"
                             targetProject="src/main/java"/>

        <!-- 9. 必需: 表配置 (至少一个) -->
        <table tableName="trace_action" domainObjectName="TraceAction" mapperName="TraceActionMapper">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>
    </context>

</generatorConfiguration>
