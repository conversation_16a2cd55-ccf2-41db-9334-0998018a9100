package com.zto.intl.cts.hk.base.facade.api.web.dto.response;

import java.io.Serializable;

public class DubboTraceActionRespDTO implements Serializable {

    private static final long serialVersionUID = -2365529853963425638L;
    /**
     * 操作状态码
     */
    private String actionCode;

    /**
     * 操作状态描述
     */
    private String actionName;

    /**
     * 轨迹状态码
     */
    private String traceAction;

    /**
     * 是否推送轨迹系统，0 否 1 是
     */
    private Boolean needPush;

    /**
     * 轨迹描述,例如：澳门仓入库
     */
    private String traceDescription;

    /**
     * 订单状态白名单
     */
    private String whiteList;

    /**
     * 订单状态黑名单
     */
    private String blackList;

    public String getActionCode() {
        return actionCode;
    }

    public void setActionCode(String actionCode) {
        this.actionCode = actionCode;
    }

    public String getActionName() {
        return actionName;
    }

    public void setActionName(String actionName) {
        this.actionName = actionName;
    }

    public String getTraceAction() {
        return traceAction;
    }

    public void setTraceAction(String traceAction) {
        this.traceAction = traceAction;
    }

    public Boolean getNeedPush() {
        return needPush;
    }

    public void setNeedPush(Boolean needPush) {
        this.needPush = needPush;
    }

    public String getTraceDescription() {
        return traceDescription;
    }

    public void setTraceDescription(String traceDescription) {
        this.traceDescription = traceDescription;
    }

    public String getWhiteList() {
        return whiteList;
    }

    public void setWhiteList(String whiteList) {
        this.whiteList = whiteList;
    }

    public String getBlackList() {
        return blackList;
    }

    public void setBlackList(String blackList) {
        this.blackList = blackList;
    }


}
