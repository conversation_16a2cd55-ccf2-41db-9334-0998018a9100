package com.zto.intl.cts.hk.base.facade.api.web;

import com.zto.intl.cts.hk.base.facade.api.web.dto.request.DubboActionMachineReqDTO;
import com.zto.intl.cts.hk.base.facade.api.web.dto.response.DubboActionMachineRespDTO;

import java.util.List;

public interface DubboActionMachineService {

    DubboActionMachineRespDTO validSingle(String actionCode, String orderStatus);

    DubboActionMachineRespDTO validBatch(String actionCode, List<DubboActionMachineReqDTO> orders);
}
