package com.zto.intl.cts.hk.base.facade.api.web.dto.request;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 状态机请求
 * @Date 2025/8/2
 * @Version 1.0
 */
public class DubboActionMachineReqDTO implements Serializable {


    private static final long serialVersionUID = 4870505818319270686L;

    private String orderStatus;

    public String getBizCode() {
        return bizCode;
    }

    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    private String bizCode;

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }


    public DubboActionMachineReqDTO(String orderStatus) {
        this.orderStatus = orderStatus;

    }
    public DubboActionMachineReqDTO(String orderStatus, String bizCode) {
        this.orderStatus = orderStatus;
        this.bizCode = bizCode;
    }
}
