package com.zto.intl.cts.hk.tds.dao.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@ToString
@Accessors(chain = true)
@Table(name = "`arrive_scan`")
public class ArriveScan implements Serializable {
    /**
     * 主键id
     */
    @Id
    @Column(name = "`id`")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 运单号
     */
    @Column(name = "`bill_code`")
    private String billCode;

    /**
     * 创建时间
     */
    @Column(name = "`gmt_create`")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @Column(name = "`gmt_modified`")
    private Date gmtModified;

    /**
     * 创建人
     */
    @Column(name = "`creator`")
    private String creator;

    /**
     * 修改人
     */
    @Column(name = "`modifier`")
    private String modifier;

    /**
     * 重量（kg）
     */
    @Column(name = "`weight`")
    private BigDecimal weight;

    /**
     * 长，CM
     */
    @Column(name = "`length`")
    private BigDecimal length;

    /**
     * 宽，CM
     */
    @Column(name = "`width`")
    private BigDecimal width;

    /**
     * 高，CM
     */
    @Column(name = "`height`")
    private BigDecimal height;

    /**
     * 收件人名称
     */
    @Column(name = "`consignee_name`")
    private String consigneeName;

    /**
     * 收件人电话
     */
    @Column(name = "`consignee_mobile`")
    private String consigneeMobile;

    /**
     * 收件人详细地址
     */
    @Column(name = "`consignee_address`")
    private String consigneeAddress;

    /**
     * 是否扫描成功，0失败，1成功
     */
    @Column(name = "`is_success`")
    private Boolean isSuccess;

    /**
     * 扫描失败原因
     */
    @Column(name = "`fail_message`")
    private String failMessage;

    /**
     * 末端派送方式：1派送，2自提
     */
    @Column(name = "`delivery_method`")
    private Integer deliveryMethod;

    /**
     * 客户单号
     */
    @Column(name = "`customer_order_no`")
    private String customerOrderNo;

    /**
     * 支付状态，0：未处理，1：支付成功，2：支付失败，3：超时关闭，4：取消关闭
     */
    @Column(name = "`pay_status`")
    private Byte payStatus;

    /**
     * 操作人
     */
    @Column(name = "`operator`")
    private String operator;

    /**
     * 操作时间
     */
    @Column(name = "`operate_time`")
    private Date operateTime;

    /**
     * 站点编码
     */
    @Column(name = "`site_code`")
    private String siteCode;

    /**
     * 站点名称
     */
    @Column(name = "`site_name`")
    private String siteName;

    /**
     * 扫描方式：1 单件扫描，2 批量扫描
     */
    @Column(name = "`scan_type`")
    private Integer scanType;

    /**
     * 推送提单至oms状态 ； 0 不推送；1待推送 ；2推送成功；3失败；4挂起不再自动重试
     */
    @Column(name = "`push_status`")
    private Byte pushStatus;

    /**
     * 推送OMS结果
     */
    @Column(name = "`push_result`")
    private String pushResult;

    /**
     * 下次执行时间
     */
    @Column(name = "`next_execute_time`")
    private Date nextExecuteTime;

    /**
     * 推送时间
     */
    @Column(name = "`push_time`")
    private Date pushTime;

    /**
     * 推送次数；超过上限次数后，不再推送
     */
    @Column(name = "`push_count`")
    private Integer pushCount;

    public static final String ID = "id";

    public static final String DB_ID = "id";

    public static final String BILL_CODE = "billCode";

    public static final String DB_BILL_CODE = "bill_code";

    public static final String GMT_CREATE = "gmtCreate";

    public static final String DB_GMT_CREATE = "gmt_create";

    public static final String GMT_MODIFIED = "gmtModified";

    public static final String DB_GMT_MODIFIED = "gmt_modified";

    public static final String CREATOR = "creator";

    public static final String DB_CREATOR = "creator";

    public static final String MODIFIER = "modifier";

    public static final String DB_MODIFIER = "modifier";

    public static final String WEIGHT = "weight";

    public static final String DB_WEIGHT = "weight";

    public static final String LENGTH = "length";

    public static final String DB_LENGTH = "length";

    public static final String WIDTH = "width";

    public static final String DB_WIDTH = "width";

    public static final String HEIGHT = "height";

    public static final String DB_HEIGHT = "height";

    public static final String CONSIGNEE_NAME = "consigneeName";

    public static final String DB_CONSIGNEE_NAME = "consignee_name";

    public static final String CONSIGNEE_MOBILE = "consigneeMobile";

    public static final String DB_CONSIGNEE_MOBILE = "consignee_mobile";

    public static final String CONSIGNEE_ADDRESS = "consigneeAddress";

    public static final String DB_CONSIGNEE_ADDRESS = "consignee_address";

    public static final String IS_SUCCESS = "isSuccess";

    public static final String DB_IS_SUCCESS = "is_success";

    public static final String FAIL_MESSAGE = "failMessage";

    public static final String DB_FAIL_MESSAGE = "fail_message";

    public static final String DELIVERY_METHOD = "deliveryMethod";

    public static final String DB_DELIVERY_METHOD = "delivery_method";

    public static final String CUSTOMER_ORDER_NO = "customerOrderNo";

    public static final String DB_CUSTOMER_ORDER_NO = "customer_order_no";

    public static final String PAY_STATUS = "payStatus";

    public static final String DB_PAY_STATUS = "pay_status";

    public static final String OPERATOR = "operator";

    public static final String DB_OPERATOR = "operator";

    public static final String OPERATE_TIME = "operateTime";

    public static final String DB_OPERATE_TIME = "operate_time";

    public static final String SITE_CODE = "siteCode";

    public static final String DB_SITE_CODE = "site_code";

    public static final String SITE_NAME = "siteName";

    public static final String DB_SITE_NAME = "site_name";

    public static final String SCAN_TYPE = "scanType";

    public static final String DB_SCAN_TYPE = "scan_type";

    public static final String PUSH_STATUS = "pushStatus";

    public static final String DB_PUSH_STATUS = "push_status";

    public static final String PUSH_RESULT = "pushResult";

    public static final String DB_PUSH_RESULT = "push_result";

    public static final String NEXT_EXECUTE_TIME = "nextExecuteTime";

    public static final String DB_NEXT_EXECUTE_TIME = "next_execute_time";

    public static final String PUSH_TIME = "pushTime";

    public static final String DB_PUSH_TIME = "push_time";

    public static final String PUSH_COUNT = "pushCount";

    public static final String DB_PUSH_COUNT = "push_count";

    private static final long serialVersionUID = 1L;
}
