package com.zto.intl.cts.hk.tds.dao.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@ToString
@Accessors(chain = true)
@Table(name = "`action_scan`")
public class ActionScan implements Serializable {
    /**
     * 主键id
     */
    @Id
    @Column(name = "`id`")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 运单号
     */
    @Column(name = "`bill_code`")
    private String billCode;

    /**
     * 客户订单号
     */
    @Column(name = "`customer_order_no`")
    private String customerOrderNo;

    /**
     * 操作状态码
     */
    @Column(name = "`action_code`")
    private String actionCode;

    /**
     * 操作状态描述
     */
    @Column(name = "`action_name`")
    private String actionName;

    /**
     * 轨迹状态码
     */
    @Column(name = "`trace_code`")
    private String traceCode;

    /**
     * 是否执行成功
     */
    @Column(name = "`is_success`")
    private Boolean isSuccess;

    /**
     * 执行失败原因
     */
    @Column(name = "`fail_message`")
    private String failMessage;

    /**
     * 操作人
     */
    @Column(name = "`operator`")
    private String operator;

    /**
     * 操作时间
     */
    @Column(name = "`operate_time`")
    private Date operateTime;

    /**
     * 扫描方式：1 单件扫描，2 批量扫描
     */
    @Column(name = "`scan_type`")
    private Boolean scanType;

    /**
     * 站点编码
     */
    @Column(name = "`site_code`")
    private String siteCode;

    /**
     * 创建时间
     */
    @Column(name = "`gmt_create`")
    private Date gmtCreate;

    /**
     * 修改时间
     */
    @Column(name = "`gmt_modified`")
    private Date gmtModified;

    /**
     * 创建人
     */
    @Column(name = "`creator`")
    private String creator;

    /**
     * 修改人
     */
    @Column(name = "`modifier`")
    private String modifier;

    /**
     * 站点名称
     */
    @Column(name = "`site_name`")
    private String siteName;

    public static final String ID = "id";

    public static final String DB_ID = "id";

    public static final String BILL_CODE = "billCode";

    public static final String DB_BILL_CODE = "bill_code";

    public static final String CUSTOMER_ORDER_NO = "customerOrderNo";

    public static final String DB_CUSTOMER_ORDER_NO = "customer_order_no";

    public static final String ACTION_CODE = "actionCode";

    public static final String DB_ACTION_CODE = "action_code";

    public static final String ACTION_NAME = "actionName";

    public static final String DB_ACTION_NAME = "action_name";

    public static final String TRACE_CODE = "traceCode";

    public static final String DB_TRACE_CODE = "trace_code";

    public static final String IS_SUCCESS = "isSuccess";

    public static final String DB_IS_SUCCESS = "is_success";

    public static final String FAIL_MESSAGE = "failMessage";

    public static final String DB_FAIL_MESSAGE = "fail_message";

    public static final String OPERATOR = "operator";

    public static final String DB_OPERATOR = "operator";

    public static final String OPERATE_TIME = "operateTime";

    public static final String DB_OPERATE_TIME = "operate_time";

    public static final String SCAN_TYPE = "scanType";

    public static final String DB_SCAN_TYPE = "scan_type";

    public static final String SITE_CODE = "siteCode";

    public static final String DB_SITE_CODE = "site_code";

    public static final String GMT_CREATE = "gmtCreate";

    public static final String DB_GMT_CREATE = "gmt_create";

    public static final String GMT_MODIFIED = "gmtModified";

    public static final String DB_GMT_MODIFIED = "gmt_modified";

    public static final String CREATOR = "creator";

    public static final String DB_CREATOR = "creator";

    public static final String MODIFIER = "modifier";

    public static final String DB_MODIFIER = "modifier";

    public static final String SITE_NAME = "siteName";

    public static final String DB_SITE_NAME = "site_name";

    private static final long serialVersionUID = 1L;
}