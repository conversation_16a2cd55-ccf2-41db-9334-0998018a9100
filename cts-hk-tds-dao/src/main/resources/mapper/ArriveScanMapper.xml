<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.intl.cts.hk.tds.dao.mapper.ArriveScanMapper">
  <resultMap id="BaseResultMap" type="com.zto.intl.cts.hk.tds.dao.entity.ArriveScan">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="bill_code" jdbcType="VARCHAR" property="billCode" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="weight" jdbcType="DECIMAL" property="weight" />
    <result column="length" jdbcType="DECIMAL" property="length" />
    <result column="width" jdbcType="DECIMAL" property="width" />
    <result column="height" jdbcType="DECIMAL" property="height" />
    <result column="consignee_name" jdbcType="VARCHAR" property="consigneeName" />
    <result column="consignee_mobile" jdbcType="VARCHAR" property="consigneeMobile" />
    <result column="consignee_address" jdbcType="VARCHAR" property="consigneeAddress" />
    <result column="is_success" jdbcType="BIT" property="isSuccess" />
    <result column="fail_message" jdbcType="VARCHAR" property="failMessage" />
    <result column="delivery_method" jdbcType="BIT" property="deliveryMethod" />
    <result column="customer_order_no" jdbcType="VARCHAR" property="customerOrderNo" />
    <result column="pay_status" jdbcType="TINYINT" property="payStatus" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="operate_time" jdbcType="TIMESTAMP" property="operateTime" />
    <result column="site_code" jdbcType="VARCHAR" property="siteCode" />
    <result column="site_name" jdbcType="VARCHAR" property="siteName" />
    <result column="scan_type" jdbcType="BIT" property="scanType" />
    <result column="push_status" jdbcType="TINYINT" property="pushStatus" />
    <result column="push_result" jdbcType="VARCHAR" property="pushResult" />
    <result column="next_execute_time" jdbcType="TIMESTAMP" property="nextExecuteTime" />
    <result column="push_time" jdbcType="TIMESTAMP" property="pushTime" />
    <result column="push_count" jdbcType="TINYINT" property="pushCount" />
  </resultMap>
</mapper>