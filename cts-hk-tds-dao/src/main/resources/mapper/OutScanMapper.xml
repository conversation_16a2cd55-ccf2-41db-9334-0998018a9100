<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zto.intl.cts.hk.tds.dao.mapper.OutScanMapper">
  <resultMap id="BaseResultMap" type="com.zto.intl.cts.hk.tds.dao.entity.OutScan">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="bill_code" jdbcType="VARCHAR" property="billCode" />
    <result column="customer_order_no" jdbcType="VARCHAR" property="customerOrderNo" />
    <result column="is_success" jdbcType="BIT" property="isSuccess" />
    <result column="fail_message" jdbcType="VARCHAR" property="failMessage" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="operate_time" jdbcType="TIMESTAMP" property="operateTime" />
    <result column="scan_type" jdbcType="BIT" property="scanType" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="site_code" jdbcType="VARCHAR" property="siteCode" />
    <result column="site_name" jdbcType="VARCHAR" property="siteName" />
  </resultMap>
</mapper>