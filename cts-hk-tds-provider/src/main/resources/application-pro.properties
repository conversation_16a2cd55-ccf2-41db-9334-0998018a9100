#Redis\u96C6\u7FA4\u8FDE\u63A5\u793A\u4F8B\u914D\u7F6E\uFF0C(\u4F7F\u7528SpringBoot\u7684\u5199\u6CD5\u5373\u53EF)
spring.redis.password=Qw9#Mp2KrT5sL!8HnD
spring.redis.cluster.nodes=r-j6cehhf3l7nuuc6z2c.redis.rds.aliyuncs.com:6379

#redis \u4E8C\u7EA7\u7F13\u5B58\u5168\u5C40\u914D\u7F6E\uFF0C(\u4F7F\u7528SpringBoot\u7684\u5199\u6CD5\u5373\u53EF)
spring.cache.redis.time-to-live=3600s
spring.cache.redis.cache-null-values=true
spring.cache.redis.use-key-prefix=true

#\u6307\u5B9Asession\u5B58\u50A8\u7684redis
titans.session.redis.cluster=r-j6cehhf3l7nuuc6z2c.redis.rds.aliyuncs.com:6379
titans.session.redis.password=Qw9#Mp2KrT5sL!8HnD

#dubbo
titans.dubbo.logger=slf4j
titans.dubbo.retries=2
titans.dubbo.delay=-1
titans.dubbo.timeout=50000
titans.dubbo.registryProtocol=zookeeper
titans.dubbo.registryAddress=zookeeper://***********:2181?backup=***********:2181,***********:2181
titans.dubbo.scanPackageName=com.zto
dubbo.registryAddress=zookeeper://***********:2181?backup=***********:2181,***********:2181
titans.dubbo.registries.zookeeper.address=zookeeper://***********:2181?backup=***********:2181,***********:2181
dubbo.registry.address=zookeeper://***********:2181?backup=***********:2181,***********:2181
titans.dubbo.titansRegistries.zookeeper.address=zookeeper://***********:2181?backup=***********:2181,***********:2181





