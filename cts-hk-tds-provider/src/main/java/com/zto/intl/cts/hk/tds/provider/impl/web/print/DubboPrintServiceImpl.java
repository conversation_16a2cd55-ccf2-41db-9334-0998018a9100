package com.zto.intl.cts.hk.tds.provider.impl.web.print;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.zto.intl.cts.hk.core.enums.ScanNoTypeEnum;
import com.zto.intl.cts.hk.tds.adapter.oms.OmsService;
import com.zto.intl.cts.hk.tds.adapter.oms.dto.request.OmsOrderLabelReqDTO;
import com.zto.intl.cts.hk.tds.adapter.oms.dto.response.OmsOrderQueryRespDTO;
import com.zto.intl.cts.hk.tds.enums.LabelTemplateCodeEnum;
import com.zto.intl.cts.hk.tds.facade.api.web.print.IDubboPrintService;
import com.zto.intl.cts.hk.tds.facade.api.web.print.dto.request.DubboPrintLabelReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.print.dto.response.DubboPrintLabelRespDTO;
import com.zto.intl.cts.hk.tds.facade.enums.PrintTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 打印服务Dubbo实现类
 * 
 * <AUTHOR>
 * @date 2025/8/1
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DubboPrintServiceImpl implements IDubboPrintService {

    private final OmsService omsService;

    @Override
    public DubboPrintLabelRespDTO printLabel(DubboPrintLabelReqDTO printLabelReqDTO) {
        log.info("面单打印Dubbo请求入参：{}", JSON.toJSONString(printLabelReqDTO));
        // 1. 获取oms订单数据
        List<OmsOrderQueryRespDTO> orders = getOmsWayBillInfo(printLabelReqDTO.getNoTypeEnum(), printLabelReqDTO.getNoList());

        // 2. 构建OMS面单请求参数
        List<OmsOrderLabelReqDTO> labelReqDTOList = buildOmsLabelRequest(orders, printLabelReqDTO);

        // 3. 调用OMS获取面单
        String labelUrl = omsService.getOmsLabel(labelReqDTOList);

        // 4. 构建响应结果
        DubboPrintLabelRespDTO respDTO = new DubboPrintLabelRespDTO();
        respDTO.setLabelUrl(labelUrl);
        log.info("面单打印成功，单号：{}, 面单URL：{}", JSON.toJSONString(printLabelReqDTO.getNoList()), labelUrl);

        return respDTO;
    }

    /**
     * 构建OMS面单请求参数
     */
    private List<OmsOrderLabelReqDTO> buildOmsLabelRequest(List<OmsOrderQueryRespDTO> orders, DubboPrintLabelReqDTO printLabelReqDTO) {
        return orders.stream()
                .map(omsOrder -> {
                    OmsOrderLabelReqDTO labelReqDTO = new OmsOrderLabelReqDTO();
                    labelReqDTO.setBillCode(omsOrder.getBillCode());
                    // 根据打印类型设置模板代码
                    labelReqDTO.setTemplateCode(getTemplateCode(printLabelReqDTO.getPrintTypeEnum()));
                    return labelReqDTO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 根据打印类型获取模板代码
     */
    private String getTemplateCode(PrintTypeEnum printTypeEnum) {
        // 根据打印类型返回对应的模板代码
        switch (printTypeEnum) {
            case LABEL:
                return LabelTemplateCodeEnum.HK_LABEL.getCode();
            case ONE_LABEL:
                return LabelTemplateCodeEnum.HK_SINGLE_LABEL.getCode();
        }

        return null;
    }

    /**
     * 获取oms分站运单信息
     */
    private List<OmsOrderQueryRespDTO> getOmsWayBillInfo(ScanNoTypeEnum noTypeEnum, List<String> noList) {
        List<OmsOrderQueryRespDTO> omsDatas;
        if(Objects.equals(ScanNoTypeEnum.CUSTOMER_ORDER_NO, noTypeEnum)) {
            omsDatas = omsService.listByCustomerOrdrNos(noList);
        } else {
            omsDatas = omsService.listByBillCodes(noList);
        }

        return omsDatas;
    }
}
