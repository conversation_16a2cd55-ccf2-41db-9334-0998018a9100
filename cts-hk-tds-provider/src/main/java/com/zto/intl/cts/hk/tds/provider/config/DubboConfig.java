//package com.zto.intl.cts.hk.tds.provider.config;
//
//import com.alibaba.dubbo.config.ApplicationConfig;
//import com.alibaba.dubbo.config.ReferenceConfig;
//import com.zto.intl.cts.hk.tds.provider.filter.DubboExceptionFilter;
//import org.springframework.boot.web.servlet.FilterRegistrationBean;
//import org.springframework.context.ApplicationContext;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
///**
// * <AUTHOR>
// * @Description
// * @Date 2025/7/31
// * @Version 1.0
// */
//@Configuration
//public class DubboConfig {
//    @Bean
//    public FilterRegistrationBean dubboFilter() {
//        ApplicationConfig applicationConfig = new ApplicationConfig();
//        applicationConfig.setName("consumer-app");
//        ReferenceConfig referenceConfig = new ReferenceConfig();
//        referenceConfig.setFilter("-exception,tubeContext,tubeException,tubeLog");
//// 注册配置类到Spring容器
//        ApplicationContext.getBean(referenceConfig);
//    }
//}
