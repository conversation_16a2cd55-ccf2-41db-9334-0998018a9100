package com.zto.intl.cts.hk.tds.provider.impl.web.actionScan;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.dubbo.config.annotation.Service;
import com.zto.intl.cts.hk.base.facade.api.web.dto.response.DubboActionMachineRespDTO;
import com.zto.intl.cts.hk.commons.util.Asserts;
import com.zto.intl.cts.hk.commons.util.BeanUtil;
import com.zto.intl.cts.hk.tds.adapter.base.ActionMachineService;
import com.zto.intl.cts.hk.tds.adapter.base.dto.request.ActionMachineValidDTO;
import com.zto.intl.cts.hk.tds.adapter.oms.OmsService;
import com.zto.intl.cts.hk.tds.adapter.oms.dto.response.OmsOrderQueryRespDTO;
import com.zto.intl.cts.hk.tds.error.ErrorEnum;
import com.zto.intl.cts.hk.tds.facade.api.web.actionScan.DubboActionScanService;
import com.zto.intl.cts.hk.tds.facade.api.web.actionScan.dto.request.DubboActionScanReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.actionScan.dto.response.DubboActionScanRespDTO;
import com.zto.intl.cts.hk.tds.service.actionScan.ActionScanService;
import com.zto.intl.cts.hk.tds.service.actionScan.dto.request.ActionScanReqDTO;
import com.zto.intl.cts.hk.tds.service.actionScan.dto.response.ActionScanRespDTO;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 状态扫描服务
 * @Date 2025/7/29
 * @Version 1.0
 */
@Service
public class DubboActionScanServiceImpl implements DubboActionScanService {

    @Autowired
    private OmsService omsService;
    @Autowired
    private ActionScanService actionScanService;
    @Autowired
    ActionMachineService actionMachineService;


    @Override
    public void scan(DubboActionScanReqDTO reqDTO){

        // oms查询订单
        OmsOrderQueryRespDTO order;
        if(StringUtil.isBlank(reqDTO.getBillCode())) {
            order = omsService.getByBillCode(reqDTO.getBillCode());
        }else {
            order = omsService.getByCustomerOrdrNo(reqDTO.getCustomerOrderNo());
        }

        // 订单不存在 ：不保存记录
        Asserts.isTrue(null != order, ErrorEnum.ActionScanError.NO_EXIST);


        // 状态机校验结果
        DubboActionMachineRespDTO valid = actionMachineService.validSingle(reqDTO.getActionCode(), order.getBillStatus());

        // 执行扫描 ：保存记录 并返回异常信息
        ActionScanReqDTO actionScanReqDTO = BeanUtil.copyProperties(reqDTO, new ActionScanReqDTO());
        if(!valid.getSuccess()){
            actionScanReqDTO.setActionMachineFailInfo(new ActionScanReqDTO.ActionMachineResult(valid.getErrorMsg()));
        }
        actionScanReqDTO.setOrderInfos(Arrays.asList(BeanUtil.copyProperties(order, new ActionScanReqDTO.OrderInfo())));
        actionScanService.actionScan(actionScanReqDTO);

    }

    @Override
    public DubboActionScanRespDTO scanBatch(DubboActionScanReqDTO reqDTO){

        // oms查询订单
        List<OmsOrderQueryRespDTO> orders;
        if(StringUtil.isBlank(reqDTO.getBillCode())) {
            orders = omsService.listByBillCodes(reqDTO.getBillCodes());
        }else {
            orders = omsService.listByCustomerOrdrNos(reqDTO.getCustomerOrderNos());
        }

        // 状态机校验
        List<ActionMachineValidDTO> machineReqDTOS = orders.stream()
                .map(e -> new ActionMachineValidDTO(e.getBillStatus(), e.getBillCode()))
                .collect(Collectors.toList());
        DubboActionMachineRespDTO valid = actionMachineService.validBatch(reqDTO.getActionCode(), machineReqDTOS);

        // 执行扫描 ：保存记录 并返回异常信息
        ActionScanReqDTO actionScanReqDTO = BeanUtil.copyProperties(reqDTO, new ActionScanReqDTO());
        if(!valid.getSuccess()){
            actionScanReqDTO.setActionMachineFailInfo(new ActionScanReqDTO.ActionMachineResult(valid.getErrorInfo().getErrorMsg(), valid.getErrorInfo().getBillNos()));
        }
        List<ActionScanReqDTO.OrderInfo> orderInfos = BeanUtil.mapAsList(orders, ActionScanReqDTO.OrderInfo.class);
        actionScanReqDTO.setOrderInfos(orderInfos);
        ActionScanRespDTO actionScanRespDTO = actionScanService.actionScanBatch(actionScanReqDTO);

        return BeanUtil.copyProperties(actionScanRespDTO, new DubboActionScanRespDTO());

    }


}
