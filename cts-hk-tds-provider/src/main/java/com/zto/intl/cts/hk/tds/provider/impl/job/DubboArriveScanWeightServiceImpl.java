package com.zto.intl.cts.hk.tds.provider.impl.job;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.zto.intl.cts.hk.tds.adapter.oms.OmsService;
import com.zto.intl.cts.hk.tds.adapter.oms.dto.request.OmsOrderWeightReqDTO;
import com.zto.intl.cts.hk.tds.adapter.oms.dto.response.OmsUpdateWeightRespDTO;
import com.zto.intl.cts.hk.tds.dao.mapper.ArriveScanMapper;
import com.zto.intl.cts.hk.tds.enums.PushStatusEnum;
import com.zto.intl.cts.hk.tds.enums.RecordCheckResultEnum;
import com.zto.intl.cts.hk.tds.facade.api.job.IDubboArriveScanWeightTaskService;
import com.zto.intl.cts.hk.tds.facade.api.job.dto.DubboArriveScanWeightTaskDTO;
import com.zto.intl.cts.hk.tds.service.arriveScan.ArriveScanService;
import com.zto.intl.cts.hk.tds.dao.entity.ArriveScan;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 到货扫描推送重量给OMS
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DubboArriveScanWeightServiceImpl implements IDubboArriveScanWeightTaskService {
    private static final int SIZE = 100;
    private static final int MAX_PUSH_COUNT = 5;
    /** push_result字段最大长度 */
    private static final int MAX_PUSH_RESULT_LENGTH = 512;

    private final OmsService omsService;
    private final ArriveScanService arriveScanService;
    private final ArriveScanMapper arriveScanMapper;

    @Override
    public List<DubboArriveScanWeightTaskDTO> selectTask() {
        log.info("开始查询待推送的到货扫描记录");

        try {
            // 查询待推送的记录，每次最多处理100条
            List<ArriveScan> pendingRecords = arriveScanService.queryPendingPushRecords(SIZE);

            if (CollectionUtils.isEmpty(pendingRecords)) {
                return new ArrayList<>();
            }

            log.info("查询到{}条待推送的到货扫描记录", pendingRecords.size());

            // 转换为任务DTO
            List<DubboArriveScanWeightTaskDTO> taskList = new ArrayList<>();
            for (ArriveScan arriveScan : pendingRecords) {
                DubboArriveScanWeightTaskDTO taskDTO = convertToTaskDTO(arriveScan);
                taskList.add(taskDTO);
            }

            return taskList;
        } catch (Exception e) {
            log.error("查询待推送的到货扫描记录异常", e);
            return new ArrayList<>();
        }
    }

    @Override
    public Boolean execute(DubboArriveScanWeightTaskDTO data) {
        log.info("开始执行推送任务，任务数据：{}", JSON.toJSONString(data));

        Long recordId = data.getId();
        String billCode = data.getBillCode();
        Integer currentPushCount = data.getPushCount() != null ? data.getPushCount() : 0;

        try {
            // 1. 先检查记录状态，如果推送次数已达上限则直接挂起
            RecordCheckResultEnum checkResult = checkAndHandleRecordStatus(recordId, currentPushCount, billCode);
            if (checkResult != RecordCheckResultEnum.CONTINUE) {
                log.info("记录状态已变更或正在处理中，跳过执行，运单号：{}，记录ID：{}", billCode, recordId);
                return true;
            }

            // 2. 构建OMS重量更新请求
            OmsOrderWeightReqDTO weightUpdateReq = buildWeightUpdateRequest(data);

            // 3. 调用OMS更新重量
            OmsUpdateWeightRespDTO omsUpdateWeightRespDTO = omsService.pushWeightAndSize(weightUpdateReq);

            // 4. 判断推送结果
            boolean pushResult = isPushSuccess(omsUpdateWeightRespDTO);
            String errorMessage = pushResult ? null : getErrorMessage(omsUpdateWeightRespDTO);

            // 5. 更新推送结果
            updatePushResult(recordId, currentPushCount, pushResult, errorMessage);

            log.info("推送重量成功，运单号：{}，记录ID：{}", billCode, recordId);
            return pushResult;
        } catch (Exception e) {
            log.error("推送重量异常，运单号：{}，记录ID：{}，错误信息：{}", billCode, recordId, e.getMessage(), e);

            // 更新推送失败结果
            updatePushResult(recordId, currentPushCount, false, e.getMessage());
            return false;
        }
    }

    /**
     * 检查记录状态并处理超过限制的记录
     */
    private RecordCheckResultEnum checkAndHandleRecordStatus(Long recordId, Integer expectedPushCount, String billCode) {
        // 通过查询数据库当前状态来判断是否可以处理
        ArriveScan currentRecord = arriveScanMapper.selectByPrimaryKey(recordId);

        // 检查推送状态是否为待推送或推送失败
        Byte pushStatus = currentRecord.getPushStatus();
        if (!PushStatusEnum.WAIT_PUSH.getCode().equals(pushStatus) &&
                !PushStatusEnum.PUSH_FAIL.getCode().equals(pushStatus)) {
            return RecordCheckResultEnum.SKIP;
        }

        // 检查推送次数是否超过限制
        Integer currentPushCount = currentRecord.getPushCount() != null ? currentRecord.getPushCount() : 0;
        if (currentPushCount >= MAX_PUSH_COUNT) {
            // 直接挂起这条记录
            suspendRecord(recordId, currentPushCount, billCode);
            return RecordCheckResultEnum.SUSPENDED;
        }

        // 检查推送次数是否与预期一致
        if (!expectedPushCount.equals(currentPushCount)) {
            return RecordCheckResultEnum.SKIP;
        }

        return RecordCheckResultEnum.CONTINUE;
    }

    /**
     * 挂起记录（推送次数超过限制）
     */
    private void suspendRecord(Long recordId, Integer currentPushCount, String billCode) {
        String suspendReason = String.format("推送次数已达上限(%d次)，系统自动挂起", currentPushCount);

        arriveScanService.updatePushInfo(
                recordId,
                currentPushCount,
                null,
                PushStatusEnum.PUSH_SUSPEND.getCode(),
                suspendReason
        );

        log.info("记录已挂起，运单号：{}，记录ID：{}，推送次数：{}", billCode, recordId, currentPushCount);
    }

    /**
     * 转换ArriveScan为任务DTO
     */
    private DubboArriveScanWeightTaskDTO convertToTaskDTO(ArriveScan arriveScan) {
        DubboArriveScanWeightTaskDTO taskDTO = new DubboArriveScanWeightTaskDTO();
        taskDTO.setId(arriveScan.getId());
        taskDTO.setBillCode(arriveScan.getBillCode());
        taskDTO.setWeight(arriveScan.getWeight());
        taskDTO.setLength(arriveScan.getLength());
        taskDTO.setWidth(arriveScan.getWidth());
        taskDTO.setHeight(arriveScan.getHeight());
        taskDTO.setPushCount(arriveScan.getPushCount());
        return taskDTO;
    }

    /**
     * 构建OMS重量更新请求
     */
    private OmsOrderWeightReqDTO buildWeightUpdateRequest(DubboArriveScanWeightTaskDTO data) {
        OmsOrderWeightReqDTO weightUpdateReq = new OmsOrderWeightReqDTO();
        weightUpdateReq.setBillCode(data.getBillCode());
        weightUpdateReq.setWeight(data.getWeight());
        weightUpdateReq.setLength(data.getLength());
        weightUpdateReq.setWidth(data.getWidth());
        weightUpdateReq.setHeight(data.getHeight());
        return weightUpdateReq;
    }

    /**
     * 更新推送结果
     */
    private void updatePushResult(Long recordId, Integer currentPushCount, boolean success, String errorMessage) {
        int newPushCount = currentPushCount + 1;
        Byte pushStatus;
        String pushResult;
        Date nextExecuteTime = null;

        if (success) {
            // 推送成功
            pushStatus = PushStatusEnum.PUSH_SUCCESS.getCode();
            pushResult = "推送成功";
        } else {
            if (newPushCount >= MAX_PUSH_COUNT) {
                // 推送次数达到5次，挂起不再重试
                pushStatus = PushStatusEnum.PUSH_SUSPEND.getCode();
                pushResult = "推送失败超过5次，挂起不再重试。最后错误：" + (errorMessage != null ? errorMessage : "未知错误");
            } else {
                // 推送失败，计算下次执行时间
                pushStatus = PushStatusEnum.PUSH_FAIL.getCode();
                pushResult = "推送失败，第" + newPushCount + "次重试。错误：" + (errorMessage != null ? errorMessage : "未知错误");
                nextExecuteTime = calculateNextExecuteTime(newPushCount);
            }
        }

        // 截取推送结果，确保不超过512字符
        pushResult = truncatePushResult(pushResult);

        // 更新数据库
        arriveScanService.updatePushInfo(recordId, newPushCount, nextExecuteTime, pushStatus, pushResult);
    }

    /**
     * 计算下次执行时间
     */
    private Date calculateNextExecuteTime(int pushCount) {
        Calendar calendar = Calendar.getInstance();

        // 指数退避：1分钟、2分钟、4分钟、8分钟
        int delayMinutes = (int) Math.pow(2, pushCount - 1);
        calendar.add(Calendar.MINUTE, delayMinutes);

        return calendar.getTime();
    }

    /**
     * 判断OMS推送是否成功
     */
    private boolean isPushSuccess(OmsUpdateWeightRespDTO omsResponse) {
        if (omsResponse.getSuccess() != null) {
            return omsResponse.getSuccess();
        }

        return false;
    }

    /**
     * 获取OMS推送失败的错误信息
     */
    private String getErrorMessage(OmsUpdateWeightRespDTO omsResponse) {
        // 获取错误信息
        String message = omsResponse.getFailReason();
        if (message != null && !message.trim().isEmpty()) {
            return message;
        }

        return "推送OMS失败";
    }

    /**
     * 截取推送结果，确保不超过最大长度
     */
    private String truncatePushResult(String pushResult) {
        if (pushResult == null) {
            return null;
        }

        if (pushResult.length() <= MAX_PUSH_RESULT_LENGTH) {
            return pushResult;
        }

        // 截取前509个字符，并添加省略号标识
        return pushResult.substring(0, MAX_PUSH_RESULT_LENGTH - 3) + "...";
    }
}
