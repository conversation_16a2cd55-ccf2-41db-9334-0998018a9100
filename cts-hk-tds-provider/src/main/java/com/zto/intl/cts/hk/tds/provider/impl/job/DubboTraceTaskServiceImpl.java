package com.zto.intl.cts.hk.tds.provider.impl.job;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.zto.intl.common.response.ResponseEnvelope;
import com.zto.intl.common.util.DateUtil;
import com.zto.intl.common.util.ExceptionUtils;
import com.zto.intl.cts.hk.commons.util.BeanUtil;
import com.zto.intl.cts.hk.core.domain.external.BaseResponse;
import com.zto.intl.cts.hk.core.exception.BizException;
import com.zto.intl.cts.hk.tds.adapter.track.TrackService;
import com.zto.intl.cts.hk.tds.adapter.track.dto.TracePushReqDTO;
import com.zto.intl.cts.hk.tds.dao.entity.TraceTask;
import com.zto.intl.cts.hk.tds.dao.mapper.TraceTaskMapper;
import com.zto.intl.cts.hk.tds.error.ErrorEnum;
import com.zto.intl.cts.hk.tds.facade.api.job.DubboTraceTaskService;
import com.zto.intl.cts.hk.tds.facade.api.job.dto.DubboTraceTaskDTO;
import com.zto.intl.os.track.bo.ZtoIntlTrackInfoBO;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

import static com.zto.intl.cts.hk.commons.constant.SysConstant.TASK_MAX_PUSH_COUNT;

/**
 * <AUTHOR>
 * @Description 推送轨迹定时任务
 * @Date 2025/8/3
 * @Version 1.0
 */
@Service
public class DubboTraceTaskServiceImpl implements DubboTraceTaskService {

    @Autowired
    private TraceTaskMapper traceTaskMapper;

    @Autowired
    TrackService trackService;

    @Override
    public List<DubboTraceTaskDTO> selectTask() {
        Example example = new Example(TraceTask.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(TraceTask.IS_SUCCESS, false);
        criteria.andLessThanOrEqualTo(TraceTask.NEXT_EXECUTE_TIME, new Date());
        criteria.andLessThan(TraceTask.PUSH_COUNT, TASK_MAX_PUSH_COUNT);

        List<TraceTask> traceTasks = traceTaskMapper.selectByExampleAndRowBounds(
                example, new RowBounds(0, 100));

        return BeanUtil.mapAsList(traceTasks, DubboTraceTaskDTO.class);
    }

    @Override
    public Boolean execute(DubboTraceTaskDTO data) {

        // 1. 执行业务逻辑
        BaseResponse response = doBusiness(data);

        // 2. 更新任务状态
        if(response.getSuccess()){
            updateTaskSuccess(data.getId(), data.getPushCount() + 1);
            return true;
        }

        updateTaskFail(data.getId(), data.getPushCount() + 1, response.getError().getMessage());
        return false;
    }

    private BaseResponse doBusiness(DubboTraceTaskDTO data) {

        TraceTask task = traceTaskMapper.selectByPrimaryKey(data.getId());
        TracePushReqDTO reqDTO = BeanUtil.copyProperties(task, new TracePushReqDTO());

       return trackService.push(reqDTO);

    }

    private void updateTaskSuccess(Long taskId, Integer pushCount) {
        TraceTask updateTask = new TraceTask();
        updateTask.setId(taskId);
        updateTask.setIsSuccess(false);
        updateTask.setPushTime(new Date());
        updateTask.setPushCount(pushCount);
        traceTaskMapper.updateByPrimaryKeySelective(updateTask);
    }

    private void updateTaskFail(Long taskId, Integer pushCount, String pushResult) {
        TraceTask updateTask = new TraceTask();
        updateTask.setId(taskId);
        updateTask.setIsSuccess(false);
        updateTask.setPushTime(new Date());
        updateTask.setPushResult(pushResult);
        updateTask.setPushCount(pushCount);
        // todo
//        updateTask.setNextExecuteTime()
        traceTaskMapper.updateByPrimaryKeySelective(updateTask);
    }
}
