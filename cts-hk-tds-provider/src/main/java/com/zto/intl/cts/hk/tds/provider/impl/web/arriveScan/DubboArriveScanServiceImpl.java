package com.zto.intl.cts.hk.tds.provider.impl.web.arriveScan;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.zto.intl.cts.hk.base.facade.api.web.dto.response.DubboActionMachineRespDTO;
import com.zto.intl.cts.hk.commons.util.BeanUtil;
import com.zto.intl.cts.hk.core.domain.web.ErrorInfo;
import com.zto.intl.cts.hk.core.domain.web.PageQuery;
import com.zto.intl.cts.hk.core.domain.web.PageResult;
import com.zto.intl.cts.hk.core.enums.ScanNoTypeEnum;
import com.zto.intl.cts.hk.core.enums.ScanTypeEnum;
import com.zto.intl.cts.hk.tds.adapter.base.ActionMachineService;
import com.zto.intl.cts.hk.tds.adapter.base.dto.request.ActionMachineValidDTO;
import com.zto.intl.cts.hk.tds.adapter.oms.OmsService;
import com.zto.intl.cts.hk.tds.adapter.oms.dto.request.OmsOrderWeightReqDTO;
import com.zto.intl.cts.hk.tds.adapter.oms.dto.response.OmsOrderQueryRespDTO;
import com.zto.intl.cts.hk.tds.adapter.oms.dto.response.OmsUpdateWeightRespDTO;
import com.zto.intl.cts.hk.tds.enums.ActionCodeEnum;
import com.zto.intl.cts.hk.tds.enums.PushStatusEnum;
import com.zto.intl.cts.hk.tds.enums.TraceEnum;
import com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.IDubboArriveScanService;
import com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.request.DubboArriveScanCheckReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.request.DubboArriveScanQueryReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.request.DubboArriveScanReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.request.DubboScanWeightReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.response.DubboArriveScanCheckRespDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.response.DubboArriveScanQueryRespDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.response.DubboArriveScanRespDTO;
import com.zto.intl.cts.hk.tds.service.arriveScan.ArriveScanService;
import com.zto.intl.cts.hk.tds.service.arriveScan.dto.request.ArriveScanQueryReqDTO;
import com.zto.intl.cts.hk.tds.service.arriveScan.dto.request.ArriveScanReqDTO;
import com.zto.intl.cts.hk.tds.service.arriveScan.dto.request.WeightUpdateReqDTO;
import com.zto.intl.cts.hk.tds.service.arriveScan.dto.response.ArriveOrderRespDTO;
import com.zto.intl.cts.hk.tds.service.arriveScan.dto.response.ArriveScanCheckRespDTO;
import com.zto.intl.cts.hk.tds.service.arriveScan.dto.response.ArriveScanQueryRespDTO;
import com.zto.intl.cts.hk.tds.service.arriveScan.dto.response.ArriveScanRespDTO;
import com.zto.intl.cts.hk.tds.service.task.trace.TraceTaskService;
import com.zto.intl.cts.hk.tds.service.task.trace.dto.request.TaskTraceAddReqDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 到货扫描服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DubboArriveScanServiceImpl implements IDubboArriveScanService {

    private final OmsService omsService;
    private final ArriveScanService arriveScanService;
    private final TraceTaskService traceTaskService;
    private final ActionMachineService actionMachineService;


    @Override
    public DubboArriveScanRespDTO arriveScan(DubboArriveScanReqDTO scanReqDTO) {
        log.info("到货扫描，请求参数：{}", JSON.toJSONString(scanReqDTO));
        // 获取oms信息
        List<OmsOrderQueryRespDTO> omsOrders = getOmsWayBillInfo(scanReqDTO.getNoTypeEnum(), scanReqDTO.getNoList());
        Map<String, ArriveOrderRespDTO> omsWayBillMap = convertToArriveOrderReqDTOMap(scanReqDTO.getNoTypeEnum(), omsOrders);
        // 到货扫描数据
        ArriveScanReqDTO arriveScanDTO = new ArriveScanReqDTO();
        BeanUtil.copyProperties(scanReqDTO, arriveScanDTO);

        // 状态机校验
        List<ActionMachineValidDTO> machineReqDTOS = omsOrders.stream()
                .map(e -> new ActionMachineValidDTO(e.getBillStatus(), e.getBillCode()))
                .collect(Collectors.toList());
        DubboActionMachineRespDTO valid = actionMachineService.validBatch(ActionCodeEnum.C10.getCode(), machineReqDTOS);
        if(!valid.getSuccess()){
            arriveScanDTO.setErrorInfo(valid.getErrorInfo());
        }

        // 执行到货扫描
        ArriveScanRespDTO arriveScanRespDTO = arriveScanService.arriveScan(arriveScanDTO, omsWayBillMap);
        // 成功单号重量、尺寸推送oms
        pushWeightAndSize(arriveScanRespDTO, arriveScanDTO);
        // 推送到件轨迹
        addArriveScanTrace(arriveScanRespDTO, arriveScanDTO);

        DubboArriveScanRespDTO dubboArriveScanRespDTO = new DubboArriveScanRespDTO();
        BeanUtil.copyProperties(arriveScanRespDTO, dubboArriveScanRespDTO);

        log.info("到货扫描结果，{}", JSON.toJSONString(dubboArriveScanRespDTO));
        return dubboArriveScanRespDTO;
    }

    @Override
    public PageResult<DubboArriveScanQueryRespDTO> queryArriveScanResult(PageQuery<DubboArriveScanQueryReqDTO> pageQuery) {
        log.info("开始查询到货扫描结果，请求参数：{}", JSON.toJSONString(pageQuery));

        // 1. 转换请求参数
        PageQuery<ArriveScanQueryReqDTO> servicePageQuery = pageQuery.convert(dubboCondition -> {
            ArriveScanQueryReqDTO serviceCondition = new ArriveScanQueryReqDTO();
            BeanUtil.copyProperties(dubboCondition, serviceCondition);
            return serviceCondition;
        });

        // 2. 调用服务层查询
        PageResult<ArriveScanQueryRespDTO> serviceResult = arriveScanService.queryArriveScanResult(servicePageQuery);

        // 3. 转换响应结果
        PageResult<DubboArriveScanQueryRespDTO> dubboResult = serviceResult.convert(this::convertToDubboQueryRespDTO);

        log.info("查询到货扫描结果完成，总记录数：{}", serviceResult.getTotal());
        return dubboResult;
    }

    @Override
    public void updateWeightAndSize(DubboScanWeightReqDTO weightReqDTO) {
        log.info("开始处理重量更新请求：{}", JSON.toJSONString(weightReqDTO));

        try {
            // 1. 转换请求参数
            WeightUpdateReqDTO weightUpdateReqDTO = convertToWeightUpdateReqDTO(weightReqDTO);

            // 2. 更新重量和尺寸
            arriveScanService.updateWeightAndSize(weightUpdateReqDTO);

            // 3. 推送OMS更新重量
            pushWeightToOms(weightReqDTO);

            log.info("重量更新处理完成，单号数量：{}", weightReqDTO.getNoList().size());
        } catch (Exception e) {
            log.error("重量更新处理失败：{}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public DubboArriveScanCheckRespDTO checkOrder(DubboArriveScanCheckReqDTO checkReqDTO) {
        log.info("开始校验订单，请求参数：{}", JSON.toJSONString(checkReqDTO));

        // 1. 批量查询订单信息
        List<OmsOrderQueryRespDTO> omsOrders = getOmsWayBillInfo(checkReqDTO.getNoTypeEnum(), checkReqDTO.getNoList());
        Map<String, ArriveOrderRespDTO> orderInfoMap = convertToArriveOrderReqDTOMap(checkReqDTO.getNoTypeEnum(), omsOrders);

        // 2. 调用service层校验逻辑
        ArriveScanCheckRespDTO serviceResult = arriveScanService.checkBillCodes(
                checkReqDTO.getNoList(),
                checkReqDTO.getNoTypeEnum(),
                checkReqDTO.getWeight(),
                orderInfoMap
        );

        // 3. 转换结果
        DubboArriveScanCheckRespDTO result = convertToScanCheckRespDTO(serviceResult);

        log.info("订单校验完成，单号数量：{}，需要二次确认：{}",
                checkReqDTO.getNoList().size(), result.getNeedConfirm());
        return result;
    }

    /**
     * 获取oms分站运单信息
     */
    private List<OmsOrderQueryRespDTO> getOmsWayBillInfo(ScanNoTypeEnum noTypeEnum, List<String> noList) {
        List<OmsOrderQueryRespDTO> omsDatas;
        if(Objects.equals(ScanNoTypeEnum.CUSTOMER_ORDER_NO, noTypeEnum)) {
            omsDatas = omsService.listByCustomerOrdrNos(noList);
        } else {
            omsDatas = omsService.listByBillCodes(noList);
        }

        return omsDatas;
    }

    /**
     * List<OmsOrderQueryRespDTO> 转Map<String, ArriveOrderRespDTO>
     */
    private Map<String, ArriveOrderRespDTO> convertToArriveOrderReqDTOMap(ScanNoTypeEnum noTypeEnum, List<OmsOrderQueryRespDTO> omsData) {
        return omsData.stream()
                .collect(Collectors.toMap(
                        Objects.equals(ScanNoTypeEnum.CUSTOMER_ORDER_NO, noTypeEnum) ? OmsOrderQueryRespDTO::getCustomerOrderNo :OmsOrderQueryRespDTO::getBillCode ,
                        this::convertToArriveOrderReqDTO,
                        (existing, replacement) -> existing));
    }

    /**
     * 将OmsOrderQueryRespDTO转换为ArriveOrderReqDTO
     */
    private ArriveOrderRespDTO convertToArriveOrderReqDTO(OmsOrderQueryRespDTO omsOrder) {
        ArriveOrderRespDTO arriveOrder = new ArriveOrderRespDTO();
        BeanUtil.copyProperties(omsOrder, arriveOrder);
        return arriveOrder;
    }

    /**
     * 推送oms称重和尺寸, 只推送单个扫描，批量扫描的数据定时任务里面推送
     */
    private void pushWeightAndSize(ArriveScanRespDTO arriveScanRespDTO, ArriveScanReqDTO arriveScanDTO) {
        List<ArriveOrderRespDTO> successOrderList = arriveScanRespDTO.getSuccessOrderList();
        if (CollectionUtils.isEmpty(successOrderList)) {
            // 如果是批量扫描，还需要处理失败的单号状态
            if (Objects.equals(ScanTypeEnum.BATCH, arriveScanDTO.getScanType())) {
                handleBatchScanFailedStatus(arriveScanRespDTO);
            }
            return;
        }

        // 提取成功的单号列表
        List<String> successBillCodes = successOrderList.stream()
                .map(ArriveOrderRespDTO::getBillCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(successBillCodes)) {
            return;
        }

        // 使用通用的推送处理方法
        boolean isSingle = Objects.equals(ScanTypeEnum.SINGLE, arriveScanDTO.getScanType());
        handleOmsPush(successBillCodes,
                      isSingle,
                      billCode -> buildOmsWeightRequestForScan(billCode, successOrderList, arriveScanDTO),
                      "扫描");

        // 批量扫描时，还需要处理失败的单号状态
        if (!isSingle) {
            handleBatchScanFailedStatus(arriveScanRespDTO);
        }
    }

    /**
     * 为扫描操作构建OMS推送请求
     */
    private OmsOrderWeightReqDTO buildOmsWeightRequestForScan(String billCode,
                                                             List<ArriveOrderRespDTO> successOrderList,
                                                             ArriveScanReqDTO arriveScanDTO) {
        // 找到对应的订单信息
        ArriveOrderRespDTO targetOrder = successOrderList.stream()
                .filter(order -> Objects.equals(billCode, order.getBillCode()))
                .findFirst()
                .orElse(null);

        if (targetOrder != null) {
            return buildOmsWeightRequest(targetOrder, arriveScanDTO);
        }

        // 如果找不到对应订单，创建基本的请求
        OmsOrderWeightReqDTO omsOrderWeightReqDTO = new OmsOrderWeightReqDTO();
        omsOrderWeightReqDTO.setBillCode(billCode);
        return omsOrderWeightReqDTO;
    }

    /**
     * 处理批量扫描失败单号的状态更新
     */
    private void handleBatchScanFailedStatus(ArriveScanRespDTO arriveScanRespDTO) {
        // 失败的单号：push_status = 0 (不推送)
        List<ErrorInfo> errors = arriveScanRespDTO.getErrors();
        if (CollectionUtils.isNotEmpty(errors)) {
            List<String> failedBillCodes = errors.stream()
                    .filter(Objects::nonNull)
                    .map(ErrorInfo::getBillNos)
                    .filter(CollectionUtils::isNotEmpty)
                    .flatMap(List::stream)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(failedBillCodes)) {
                arriveScanService.batchUpdatePushStatus(failedBillCodes, PushStatusEnum.NOT_PUSH.getCode());
                log.info("批量扫描失败单号更新为不推送状态，数量：{}", failedBillCodes.size());
            }
        }
    }

    /**
     * 构建OMS重量推送请求
     */
    private OmsOrderWeightReqDTO buildOmsWeightRequest(ArriveOrderRespDTO successOrder, ArriveScanReqDTO arriveScanDTO) {
        OmsOrderWeightReqDTO omsOrderWeightReqDTO = new OmsOrderWeightReqDTO();
        omsOrderWeightReqDTO.setBillCode(successOrder.getBillCode());
        omsOrderWeightReqDTO.setWeight(arriveScanDTO.getWeight());
        omsOrderWeightReqDTO.setLength(arriveScanDTO.getLength());
        omsOrderWeightReqDTO.setWidth(arriveScanDTO.getWidth());
        omsOrderWeightReqDTO.setHeight(arriveScanDTO.getHeight());
        return omsOrderWeightReqDTO;
    }

    /**
     * 到件轨迹
     */
    private void addArriveScanTrace(ArriveScanRespDTO arriveScanRespDTO, ArriveScanReqDTO arriveScanDTO) {
        List<ArriveOrderRespDTO> successBillList = arriveScanRespDTO.getSuccessOrderList();
        List<TaskTraceAddReqDTO> taskList = new ArrayList<>();
        for(ArriveOrderRespDTO arriveOrderReqDTO : successBillList) {
            TaskTraceAddReqDTO taskTraceAddReqDTO = new TaskTraceAddReqDTO();
            taskTraceAddReqDTO.setBillCode(arriveOrderReqDTO.getBillCode());
            taskTraceAddReqDTO.setCustomerOrderNo(arriveOrderReqDTO.getCustomerOrderNo());
            taskTraceAddReqDTO.setActionCode(TraceEnum.C10.getActionCode());
            taskTraceAddReqDTO.setActionName(TraceEnum.C10.getActionName());
            taskTraceAddReqDTO.setTraceAction(TraceEnum.C10.getTraceAction());
            taskTraceAddReqDTO.setSiteCode(arriveScanDTO.getSiteCode());
            taskTraceAddReqDTO.setSiteName(arriveScanDTO.getSiteName());
            taskTraceAddReqDTO.setOperator(arriveScanDTO.getOperator());
            taskTraceAddReqDTO.setOperateTime(new Date());

            taskList.add(taskTraceAddReqDTO);
        }

        traceTaskService.addTaskBatch(taskList);
    }

    /**
     * 转换DubboScanWeightReqDTO为WeightUpdateReqDTO
     */
    private WeightUpdateReqDTO convertToWeightUpdateReqDTO(DubboScanWeightReqDTO weightReqDTO) {
        WeightUpdateReqDTO weightUpdateReqDTO = new WeightUpdateReqDTO();
        BeanUtil.copyProperties(weightReqDTO, weightUpdateReqDTO);
        return weightUpdateReqDTO;
    }

    /**
     * 推送重量到OMS
     */
    private void pushWeightToOms(DubboScanWeightReqDTO weightReqDTO) {
        if (CollectionUtils.isEmpty(weightReqDTO.getNoList())) {
            return;
        }

        // 使用通用的推送处理方法
        handleOmsPush(weightReqDTO.getNoList(),
                      weightReqDTO.getNoList().size() == 1,
                      no -> buildOmsWeightRequestForUpdate(no, weightReqDTO),
                      "重量更新");
    }

    /**
     * 构建重量更新的OMS推送请求
     */
    private OmsOrderWeightReqDTO buildOmsWeightRequestForUpdate(String billCode, DubboScanWeightReqDTO weightReqDTO) {
        OmsOrderWeightReqDTO omsOrderWeightReqDTO = new OmsOrderWeightReqDTO();

        // 根据单号类型设置运单号
        if (Objects.equals(ScanNoTypeEnum.BILL_CODE, weightReqDTO.getNoTypeEnum())) {
            omsOrderWeightReqDTO.setBillCode(billCode);
        } else {
            // 如果是客户单号，需要通过OMS查询获取运单号
            OmsOrderQueryRespDTO omsOrder = omsService.getByCustomerOrdrNo(billCode);
            if (omsOrder != null) {
                omsOrderWeightReqDTO.setBillCode(omsOrder.getBillCode());
            }
        }

        omsOrderWeightReqDTO.setWeight(weightReqDTO.getWeight());
        omsOrderWeightReqDTO.setLength(weightReqDTO.getLength());
        omsOrderWeightReqDTO.setWidth(weightReqDTO.getWidth());
        omsOrderWeightReqDTO.setHeight(weightReqDTO.getHeight());

        return omsOrderWeightReqDTO;
    }

    /**
     * 转换ArriveScanQueryRespDTO为DubboArriveScanQueryRespDTO
     */
    private DubboArriveScanQueryRespDTO convertToDubboQueryRespDTO(ArriveScanQueryRespDTO serviceResp) {
        DubboArriveScanQueryRespDTO dubboResp = new DubboArriveScanQueryRespDTO();
        BeanUtil.copyProperties(serviceResp, dubboResp);
        return dubboResp;
    }

    /**
     * 通用的OMS推送处理方法
     */
    private void handleOmsPush(List<String> billCodes,
                              boolean isSingle,
                              Function<String, OmsOrderWeightReqDTO> requestBuilder,
                              String operationType) {
        if (CollectionUtils.isEmpty(billCodes)) {
            return;
        }

        if (isSingle) {
            // 单个操作：推送OMS并更新推送结果
            handleSingleOmsPush(billCodes.get(0), requestBuilder, operationType);
        } else {
            // 批量操作：不推送OMS，只更新推送状态为待推送
            handleBatchOmsPushStatus(billCodes, operationType);
        }
    }

    /**
     * 处理单个操作的OMS推送
     */
    private void handleSingleOmsPush(String billCode,
                                   Function<String, OmsOrderWeightReqDTO> requestBuilder,
                                   String operationType) {
        try {
            // 构建OMS推送请求
            OmsOrderWeightReqDTO omsOrderWeightReqDTO = requestBuilder.apply(billCode);

            // 推送到OMS
            OmsUpdateWeightRespDTO omsUpdateWeightRespDTO = omsService.pushWeightAndSize(omsOrderWeightReqDTO);

            // 根据推送结果更新推送状态
            if (omsUpdateWeightRespDTO.getSuccess()) {
                // 推送成功：push_status = 2
                arriveScanService.updatePushResultWithMessage(billCode,
                        PushStatusEnum.PUSH_SUCCESS.getCode(), operationType + "推送成功");
                log.info("单个{}OMS推送成功，单号：{}", operationType, billCode);
            } else {
                // 推送失败：push_status = 3
                String failReason = omsUpdateWeightRespDTO.getFailReason();
                arriveScanService.updatePushResultWithMessage(billCode,
                        PushStatusEnum.PUSH_FAIL.getCode(), operationType + "推送失败：" + failReason);
                log.warn("单个{}OMS推送失败，单号：{}，失败原因：{}", operationType, billCode, failReason);
            }
        } catch (Exception e) {
            // 推送异常：push_status = 3
            arriveScanService.updatePushResultWithMessage(billCode,
                    PushStatusEnum.PUSH_FAIL.getCode(), operationType + "推送异常：" + e.getMessage());
            log.error("单个{}OMS推送异常，单号：{}", operationType, billCode, e);
        }
    }

    /**
     * 处理批量操作的推送状态更新
     */
    private void handleBatchOmsPushStatus(List<String> billCodes, String operationType) {
        // 批量操作：所有单号都设置为待推送状态 push_status = 1
        arriveScanService.batchUpdatePushStatus(billCodes, PushStatusEnum.WAIT_PUSH.getCode());
        log.info("批量{}单号更新为待推送状态，数量：{}", operationType, billCodes.size());
    }

    /**
     * 转换ScanCheckResultDTO为DubboScanCheckRespDTO
     */
    private DubboArriveScanCheckRespDTO convertToScanCheckRespDTO(ArriveScanCheckRespDTO serviceResult) {
        DubboArriveScanCheckRespDTO result = new DubboArriveScanCheckRespDTO();
        result.setNeedConfirm(serviceResult.getNeedConfirm());

        if (CollectionUtils.isNotEmpty(serviceResult.getErrors())) {
            List<ErrorInfo> errors = serviceResult.getErrors().stream()
                    .map(serviceError -> new ErrorInfo(
                            serviceError.getBillNos(),
                            serviceError.getErrorMsg()
                    )).collect(Collectors.toList());
            result.setErrors(errors);
        }
        return result;
    }
}
