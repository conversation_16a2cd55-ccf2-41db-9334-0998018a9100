<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cts-hk-tds</artifactId>
        <groupId>com.zto</groupId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cts-hk-tds-provider</artifactId>

    <dependencies>

        <dependency>
            <groupId>com.zto</groupId>
            <artifactId>cts-hk-tds-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zto</groupId>
            <artifactId>cts-hk-tds-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zto</groupId>
            <artifactId>cts-hk-tds-adapter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zto.titans</groupId>
            <artifactId>titans-endpoint</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>



    </dependencies>

    <build>
        <resources>
            <resource>
                <targetPath>${project.build.directory}/classes</targetPath>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.properties</include>
                </includes>
            </resource>
            <resource>
                <targetPath>${project.build.directory}/classes</targetPath>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>META-INF/**</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>


        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>

                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>com.zto.zsmp</groupId>
                <artifactId>zsmp-doc-maven-plugin</artifactId>
                <version>0.0.2-SNAPSHOT</version>
                <executions>
                    <execution>
                        <phase>install</phase>
                        <goals>
                            <goal>zsmpDoc</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <serverUrl>https://zsmp.dev.ztosys.com</serverUrl>
                    <token>cb17333fa2214de29c436ec94244d048</token>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
