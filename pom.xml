<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.zto</groupId>
    <artifactId>cts-hk-base</artifactId>
    <packaging>pom</packaging>
    <version>1.0.1-SNAPSHOT</version>

    <modules>

        <module>cts-hk-base-provider</module>
        <module>cts-hk-base-facade</module>
        <module>cts-hk-base-service</module>
        <module>cts-hk-base-dao</module>
    </modules>

    <parent>
        <groupId>com.zto.titans</groupId>
        <artifactId>titans-parent</artifactId>
        <version>2.9.3.13.RELEASE</version>
    </parent>

    <!-- 统一配置 Java 版本 -->
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <cts.hk.version>1.0.1-SNAPSHOT</cts.hk.version>
        <intl.common.version>0.0.46-SNAPSHOT</intl.common.version>
    </properties>


    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.zto</groupId>
                <artifactId>cts-hk-commons</artifactId>
                <version>${cts.hk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zto</groupId>
                <artifactId>cts-hk-base-service</artifactId>
                <version>${cts.hk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zto</groupId>
                <artifactId>cts-hk-base-dao</artifactId>
                <version>${cts.hk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zto</groupId>
                <artifactId>cts-hk-base-facade</artifactId>
                <version>${cts.hk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zto</groupId>
                <artifactId>cts-hk-core</artifactId>
                <version>${cts.hk.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>


</project>
