<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.zto</groupId>
    <artifactId>cts-hk-tds</artifactId>
    <packaging>pom</packaging>
    <version>1.0.1-SNAPSHOT</version>

    <modules>

        <module>cts-hk-tds-service</module>
        <module>cts-hk-tds-facade</module>
        <module>cts-hk-tds-provider</module>
        <module>cts-hk-tds-dao</module>
        <module>cts-hk-tds-adapter</module>
    </modules>

    <parent>
        <groupId>com.zto.titans</groupId>
        <artifactId>titans-parent</artifactId>
        <version>2.9.3.13.RELEASE</version>
    </parent>

    <!-- 统一配置 Java 版本 -->
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <cts.hk.version>1.0.1-SNAPSHOT</cts.hk.version>
        <intl.common.version>0.0.59-SNAPSHOT</intl.common.version>
        <zsmp.version>0.0.1-SNAPSHOT</zsmp.version>
        <oms-overseas-interface.version>3.93.0-SNAPSHOT</oms-overseas-interface.version>
        <track-overseas-interface.version>5.47.0-1-RELEASE</track-overseas-interface.version>
    </properties>


    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.zto</groupId>
                <artifactId>cts-hk-tds-service</artifactId>
                <version>${cts.hk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zto</groupId>
                <artifactId>cts-hk-tds-dao</artifactId>
                <version>${cts.hk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zto</groupId>
                <artifactId>cts-hk-tds-adapter</artifactId>
                <version>${cts.hk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zto</groupId>
                <artifactId>cts-hk-tds-facade</artifactId>
                <version>${cts.hk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zto</groupId>
                <artifactId>cts-hk-core</artifactId>
                <version>${cts.hk.version}</version>
            </dependency>


            <dependency>
                <groupId>com.zto</groupId>
                <artifactId>cts-hk-commons</artifactId>
                <version>${cts.hk.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zto</groupId>
                <artifactId>cts-hk-base-facade</artifactId>
                <version>${cts.hk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zto.intl</groupId>
                <artifactId>zto-international-common</artifactId>
                <version>${intl.common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zto.zsmp</groupId>
                <artifactId>zsmp-annotation</artifactId>
                <version>${zsmp.version}</version>
            </dependency>


            <dependency>
                <groupId>com.zto.intl</groupId>
                <artifactId>intl-oms-overseas-interface</artifactId>
                <version>${oms-overseas-interface.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zto.intl</groupId>
                <artifactId>intl-track-overseas-interface</artifactId>
                <version>${track-overseas-interface.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


</project>
