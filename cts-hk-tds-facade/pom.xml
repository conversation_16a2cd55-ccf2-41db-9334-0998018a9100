<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cts-hk-tds</artifactId>
        <groupId>com.zto</groupId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cts-hk-tds-facade</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.zto</groupId>
            <artifactId>cts-hk-core</artifactId>
        </dependency>
    </dependencies>


    <distributionManagement>
        <repository>
            <id>releases</id>
            <name>libs-release</name>
            <url>https://jfrog.dev.ztosys.com/artifactory/libs-release/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>libs-snapshot</name>
            <url>https://jfrog.dev.ztosys.com/artifactory/libs-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
