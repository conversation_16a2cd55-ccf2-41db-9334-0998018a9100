package com.zto.intl.cts.hk.tds.facade.api.web.actionScan.dto.response;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 批量扫描响应
 * @Date 2025/7/30
 * @Version 1.0
 */

public class DubboActionScanRespDTO implements Serializable {

    private static final long serialVersionUID = 1482036332903492043L;
    /**
     * 成功数
     */
    private Integer successCount;

    /**
     * 失败数
     */
    private Integer failCount;

    /**
     * 详细失败信息：
     * key: 失败原因，比如 “以下单号不存在”
     * value  单号list
     */
    private Map<String, List<String>> failMessage;


    public DubboActionScanRespDTO() {
    }

    public Integer getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(Integer successCount) {
        this.successCount = successCount;
    }

    public Integer getFailCount() {
        return failCount;
    }

    public void setFailCount(Integer failCount) {
        this.failCount = failCount;
    }

    public Map<String, List<String>> getFailMessage() {
        return failMessage;
    }

    public void setFailMessage(Map<String, List<String>> failMessage) {
        this.failMessage = failMessage;
    }
}
