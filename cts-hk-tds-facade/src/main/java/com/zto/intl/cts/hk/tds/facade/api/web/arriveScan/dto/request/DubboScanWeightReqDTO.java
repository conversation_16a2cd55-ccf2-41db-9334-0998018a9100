package com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.request;

import com.zto.intl.cts.hk.core.enums.ScanNoTypeEnum;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 20225/7/24
 */
public class DubboScanWeightReqDTO implements Serializable {

    private static final long serialVersionUID = 23520201632434531L;

    /**
     * 单号类型
     */
    private ScanNoTypeEnum noTypeEnum;

    /** 单号 **/
    private List<String> noList;

    /** 重量 单位kg **/
    private BigDecimal weight;

    /** 长 单位cm **/
    private BigDecimal length;

    /** 宽 单位cm **/
    private BigDecimal width;

    /** 高 单位cm **/
    private BigDecimal height;

    /** 操作人 **/
    private String operator;

    public ScanNoTypeEnum getNoTypeEnum() {
        return noTypeEnum;
    }

    public void setNoTypeEnum(ScanNoTypeEnum noTypeEnum) {
        this.noTypeEnum = noTypeEnum;
    }

    public List<String> getNoList() {
        return noList;
    }

    public void setNoList(List<String> noList) {
        this.noList = noList;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}
