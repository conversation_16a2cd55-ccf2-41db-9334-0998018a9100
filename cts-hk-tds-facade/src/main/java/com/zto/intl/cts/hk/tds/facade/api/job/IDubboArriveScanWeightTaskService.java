package com.zto.intl.cts.hk.tds.facade.api.job;

import com.zto.intl.cts.hk.tds.facade.api.job.dto.DubboArriveScanWeightTaskDTO;
import java.util.List;

/**
 * 到货扫描推送重量给OMS定时任务
 */
public interface IDubboArriveScanWeightTaskService extends DubboTaskService<DubboArriveScanWeightTaskDTO> {

    /**
     * 查询待推送的记录
     */
    List<DubboArriveScanWeightTaskDTO> selectTask();

    /**
     * 执行推送
     */
    Boolean execute(DubboArriveScanWeightTaskDTO data);

}
