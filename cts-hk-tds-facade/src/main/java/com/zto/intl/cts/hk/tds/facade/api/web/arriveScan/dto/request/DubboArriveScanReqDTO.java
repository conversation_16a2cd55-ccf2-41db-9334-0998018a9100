package com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.request;

import com.zto.intl.cts.hk.core.enums.ScanNoTypeEnum;
import com.zto.intl.cts.hk.core.enums.ScanTypeEnum;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/24
 */
public class DubboArriveScanReqDTO implements Serializable {

    private static final long serialVersionUID = 235202016345234535L;

    /**
     * 单号类型
     */
    private ScanNoTypeEnum noTypeEnum;

    /** 单号 **/
    private List<String> noList;

    /** 重量 单位kg **/
    private BigDecimal weight;

    /** 长 单位cm **/
    private BigDecimal length;

    /** 宽 单位cm **/
    private BigDecimal width;

    /** 高 单位cm **/
    private BigDecimal height;

    /** 操作人 **/
    private String operator;

    /** 操作人所属站点 **/
    private String siteCode;

    /** 操作人所属站点名称 **/
    private String siteName;

    /** 扫描类型 1-单个扫描 2-批量扫描 **/
    private ScanTypeEnum scanType;

    public ScanNoTypeEnum getNoTypeEnum() {
        return noTypeEnum;
    }

    public void setNoTypeEnum(ScanNoTypeEnum noTypeEnum) {
        this.noTypeEnum = noTypeEnum;
    }

    public List<String> getNoList() {
        return noList;
    }

    public void setNoList(List<String> noList) {
        this.noList = noList;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getSiteCode() {
        return siteCode;
    }

    public void setSiteCode(String siteCode) {
        this.siteCode = siteCode;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public ScanTypeEnum getScanType() {
        return scanType;
    }

    public void setScanType(ScanTypeEnum scanType) {
        this.scanType = scanType;
    }
}
