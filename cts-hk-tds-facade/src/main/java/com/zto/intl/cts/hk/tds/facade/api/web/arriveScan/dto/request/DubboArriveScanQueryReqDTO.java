package com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.request;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 20225/7/24
 */
public class DubboArriveScanQueryReqDTO implements Serializable {

    private static final long serialVersionUID = 2352599016342335934L;

    /** 开始扫描时间 **/
    private Date startTime;

    /** 结束扫描时间 **/
    private Date endTime;

    /** 支付状态 **/
    private Byte payStatus;

    /** 扫描结果 1-成功 0-失败 **/
    private Boolean scanResult;

    /** id集合 - 勾选导出 **/
    private List<Long> ids;

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Boolean getScanResult() {
        return scanResult;
    }

    public void setScanResult(Boolean scanResult) {
        this.scanResult = scanResult;
    }

    public Byte getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(Byte payStatus) {
        this.payStatus = payStatus;
    }

    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }
}
