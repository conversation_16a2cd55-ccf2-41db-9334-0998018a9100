package com.zto.intl.cts.hk.tds.facade.api.web.print.dto.request;

import com.zto.intl.cts.hk.core.enums.ScanNoTypeEnum;
import com.zto.intl.cts.hk.tds.facade.enums.PrintTypeEnum;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 20225/7/24
 */
public class DubboPrintLabelReqDTO implements Serializable {
    private static final long serialVersionUID = 2352020122345314535L;

    /** 单号类型 运单号: BILL_CODE, 客户单号：CUSTOMER_ORDER_NO **/
    private ScanNoTypeEnum noTypeEnum;

    /** 单号 **/
    private List<String> noList;

    /** 打印类型 **/
    private PrintTypeEnum printTypeEnum;

    public ScanNoTypeEnum getNoTypeEnum() {
        return noTypeEnum;
    }

    public void setNoTypeEnum(ScanNoTypeEnum noTypeEnum) {
        this.noTypeEnum = noTypeEnum;
    }

    public List<String> getNoList() {
        return noList;
    }

    public void setNoList(List<String> noList) {
        this.noList = noList;
    }

    public PrintTypeEnum getPrintTypeEnum() {
        return printTypeEnum;
    }

    public void setPrintTypeEnum(PrintTypeEnum printTypeEnum) {
        this.printTypeEnum = printTypeEnum;
    }
}
