package com.zto.intl.cts.hk.tds.facade.api.web.actionScan.dto.response;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 状态扫描分页查询
 * @Date 2025/7/30
 * @Version 1.0
 */

public class DubboActionScanPageRespDTO implements Serializable {

    private static final long serialVersionUID = 7107196992075209972L;

    /**
     * 运单号
     */
    private String billCode;

    /**
     * 客户订单号
     */
    private String customerOrderNo;

    /**
     * 操作状态码
     */
    private String actionCode;

    /**
     * 操作状态描述
     */
    private String actionName;

    /**
     * 是否执行成功
     */
    private Boolean isSuccess;

    /**
     * 执行失败原因
     */
    private String failMessage;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    private Date operateTime;

    public String getBillCode() {
        return billCode;
    }

    public void setBillCode(String billCode) {
        this.billCode = billCode;
    }

    public String getCustomerOrderNo() {
        return customerOrderNo;
    }

    public void setCustomerOrderNo(String customerOrderNo) {
        this.customerOrderNo = customerOrderNo;
    }

    public String getActionCode() {
        return actionCode;
    }

    public void setActionCode(String actionCode) {
        this.actionCode = actionCode;
    }

    public String getActionName() {
        return actionName;
    }

    public void setActionName(String actionName) {
        this.actionName = actionName;
    }

    public Boolean getSuccess() {
        return isSuccess;
    }

    public void setSuccess(Boolean success) {
        isSuccess = success;
    }

    public String getFailMessage() {
        return failMessage;
    }

    public void setFailMessage(String failMessage) {
        this.failMessage = failMessage;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Date operateTime) {
        this.operateTime = operateTime;
    }
}
