package com.zto.intl.cts.hk.tds.facade.api.web.outScan.dto.request;

import com.zto.intl.cts.hk.core.enums.ScanNoTypeEnum;
import com.zto.intl.cts.hk.core.enums.ScanTypeEnum;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/24
 */
public class DubboOutScanReqDTO implements Serializable {
    private static final long serialVersionUID = 2351021192342334420L;
    /**
     * 单号类型
     * 运单号: BILL_CODE,
     * 客户单号：CUSTOMER_ORDER_NO
     */
    private ScanNoTypeEnum noTypeEnum;

    /** 单号 **/
    private List<String> noList;

    /** 操作人 **/
    private String operator;

    /** 操作人所属站点 **/
    private String siteCode;

    /** 操作人所属站点名称 **/
    private String siteName;

    /** 扫描类型 1-单个扫描 2-批量扫描 **/
    private ScanTypeEnum scanType;

    public ScanNoTypeEnum getNoTypeEnum() {
        return noTypeEnum;
    }

    public void setNoTypeEnum(ScanNoTypeEnum noTypeEnum) {
        this.noTypeEnum = noTypeEnum;
    }

    public List<String> getNoList() {
        return noList;
    }

    public void setNoList(List<String> noList) {
        this.noList = noList;
    }

    public ScanTypeEnum getScanType() {
        return scanType;
    }

    public void setScanType(ScanTypeEnum scanType) {
        this.scanType = scanType;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getSiteCode() {
        return siteCode;
    }

    public void setSiteCode(String siteCode) {
        this.siteCode = siteCode;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }
}
