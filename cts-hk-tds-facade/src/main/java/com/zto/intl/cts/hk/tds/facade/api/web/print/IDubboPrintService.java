package com.zto.intl.cts.hk.tds.facade.api.web.print;

import com.zto.intl.cts.hk.tds.facade.api.web.print.dto.request.DubboPrintLabelReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.print.dto.response.DubboPrintLabelRespDTO;

/**
 * 打印服务Dubbo接口
 * 
 * <AUTHOR>
 * @date 2025/8/1
 */
public interface IDubboPrintService {

    /**
     * 打印面单
     */
    DubboPrintLabelRespDTO printLabel(DubboPrintLabelReqDTO printLabelReqDTO);
}
