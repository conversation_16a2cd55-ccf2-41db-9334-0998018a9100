package com.zto.intl.cts.hk.tds.facade.api.web.outScan.dto.request;

import com.zto.intl.cts.hk.core.enums.ScanNoTypeEnum;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/24
 */
public class DubboOutScanCheckReqDTO implements Serializable {
    private static final long serialVersionUID = 2321011199342634475L;
    /**
     * 单号类型
     * 运单号: BILL_CODE,
     * 客户单号：CUSTOMER_ORDER_NO
     */
    private ScanNoTypeEnum noTypeEnum;

    /** 单号 **/
    private List<String> noList;

    public ScanNoTypeEnum getNoTypeEnum() {
        return noTypeEnum;
    }

    public void setNoTypeEnum(ScanNoTypeEnum noTypeEnum) {
        this.noTypeEnum = noTypeEnum;
    }

    public List<String> getNoList() {
        return noList;
    }

    public void setNoList(List<String> noList) {
        this.noList = noList;
    }
}
