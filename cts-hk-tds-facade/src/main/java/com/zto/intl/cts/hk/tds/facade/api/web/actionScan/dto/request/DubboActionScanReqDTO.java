package com.zto.intl.cts.hk.tds.facade.api.web.actionScan.dto.request;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 状态扫描请求
 * @Date 2025/7/29
 * @Version 1.0
 */
public class DubboActionScanReqDTO implements Serializable {
    private static final long serialVersionUID = 2352599016345775935L;

    private String billCode;
    private List<String> billCodes;
    private String customerOrderNo;
    private List<String> customerOrderNos;
    private String actionCode;
    private String actionName;
    private String userCode;
    private String siteCode;

    public String getBillCode() {
        return billCode;
    }

    public void setBillCode(String billCode) {
        this.billCode = billCode;
    }

    public List<String> getBillCodes() {
        return billCodes;
    }

    public void setBillCodes(List<String> billCodes) {
        this.billCodes = billCodes;
    }

    public String getCustomerOrderNo() {
        return customerOrderNo;
    }

    public void setCustomerOrderNo(String customerOrderNo) {
        this.customerOrderNo = customerOrderNo;
    }

    public List<String> getCustomerOrderNos() {
        return customerOrderNos;
    }

    public void setCustomerOrderNos(List<String> customerOrderNos) {
        this.customerOrderNos = customerOrderNos;
    }

    public String getActionCode() {
        return actionCode;
    }

    public void setActionCode(String actionCode) {
        this.actionCode = actionCode;
    }

    public String getActionName() {
        return actionName;
    }

    public void setActionName(String actionName) {
        this.actionName = actionName;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getSiteCode() {
        return siteCode;
    }

    public void setSiteCode(String siteCode) {
        this.siteCode = siteCode;
    }
}
