package com.zto.intl.cts.hk.tds.facade.api.web.outScan.dto.response;

import com.zto.intl.cts.hk.core.domain.web.ErrorInfo;

import java.io.Serializable;
import java.util.List;

/**
 * 出货扫描校验响应DTO
 * 
 * <AUTHOR>
 * @date 2025/8/1
 */
public class DubboOutScanCheckRespDTO implements Serializable {
    private static final long serialVersionUID = 2351021192342334420L;
    /**
     * 是否需要二次确认弹窗
     */
    private Boolean needConfirm = false;

    /**
     * 校验错误信息列表
     */
    private List<ErrorInfo> errors;

    public Boolean getNeedConfirm() {
        return needConfirm;
    }

    public void setNeedConfirm(Boolean needConfirm) {
        this.needConfirm = needConfirm;
    }

    public List<ErrorInfo> getErrors() {
        return errors;
    }

    public void setErrors(List<ErrorInfo> errors) {
        this.errors = errors;
    }
}
