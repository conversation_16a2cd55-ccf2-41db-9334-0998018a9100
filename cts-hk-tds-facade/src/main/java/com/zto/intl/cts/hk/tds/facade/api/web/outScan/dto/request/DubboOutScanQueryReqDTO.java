package com.zto.intl.cts.hk.tds.facade.api.web.outScan.dto.request;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 20225/7/24
 */
public class DubboOutScanQueryReqDTO implements Serializable {
    private static final long serialVersionUID = 2351021192042234410L;
    /**
     * 开始扫描时间
     */
    private Date startTime;

    /** 结束扫描时间 **/
    private Date endTime;

    /** 扫描结果 1-成功 0-失败 **/
    private Boolean scanResult;

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Boolean getScanResult() {
        return scanResult;
    }

    public void setScanResult(Boolean scanResult) {
        this.scanResult = scanResult;
    }
}
