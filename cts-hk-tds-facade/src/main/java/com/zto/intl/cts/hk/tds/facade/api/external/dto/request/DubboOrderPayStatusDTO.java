package com.zto.intl.cts.hk.tds.facade.api.external.dto.request;

import java.io.Serializable;

/**
 * 收款完成状态回传
 */
public class DubboOrderPayStatusDTO implements Serializable {

    private static final long serialVersionUID = 2352199016345785935L;

    /** 运单号 **/
    private String billCode;

    /** 支付状态 **/
    private Byte payStatus;

    public String getBillCode() {
        return billCode;
    }

    public void setBillCode(String billCode) {
        this.billCode = billCode;
    }

    public Byte getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(Byte payStatus) {
        this.payStatus = payStatus;
    }
}
