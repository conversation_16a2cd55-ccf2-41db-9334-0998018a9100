package com.zto.intl.cts.hk.tds.facade.api.job.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 到货扫描推送重量给oms 任务
 */
public class DubboArriveScanWeightTaskDTO extends DubboTaskParentDTO implements Serializable {

    /** 运单号 **/
    private String billCode;

    /** 重量kg **/
    private BigDecimal weight;

    /** 长CM **/
    private BigDecimal length;

    /** 宽CM **/
    private BigDecimal width;

    /** 高CM **/
    private BigDecimal height;

    /**
     * 推送次数
     */
    private Integer pushCount;

    public String getBillCode() {
        return billCode;
    }

    public void setBillCode(String billCode) {
        this.billCode = billCode;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public Integer getPushCount() {
        return pushCount;
    }

    public void setPushCount(Integer pushCount) {
        this.pushCount = pushCount;
    }
}
