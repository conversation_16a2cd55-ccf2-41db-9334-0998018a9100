package com.zto.intl.cts.hk.tds.facade.api.web.outScan.dto.response;

import java.io.Serializable;
import java.util.Date;

/**
 * 出货扫描查询结果
 * <AUTHOR>
 * @date 2025/07/24
 */
public class DubboOutScanQueryRespDTO implements Serializable {
    private static final long serialVersionUID = 2351432182342234490L;
    /** 序列 **/
    private Integer seq;

    /** 扫描时间 **/
    private Date operateTime;

    /** 运单号 **/
    private String billCode;

    /** 客户单号 **/
    private String customerOrderNo;

    /** 扫描结果 **/
    private Boolean isSuccess;

    /** 扫描结果描述 **/
    private String scanResultDesc;

    /** 扫描失败原因 **/
    private String failMessage;

    /** 操作人 **/
    private String operator;

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public Date getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Date operateTime) {
        this.operateTime = operateTime;
    }

    public String getBillCode() {
        return billCode;
    }

    public void setBillCode(String billCode) {
        this.billCode = billCode;
    }

    public String getCustomerOrderNo() {
        return customerOrderNo;
    }

    public void setCustomerOrderNo(String customerOrderNo) {
        this.customerOrderNo = customerOrderNo;
    }

    public Boolean getSuccess() {
        return isSuccess;
    }

    public void setSuccess(Boolean success) {
        isSuccess = success;
    }

    public String getScanResultDesc() {
        return scanResultDesc;
    }

    public void setScanResultDesc(String scanResultDesc) {
        this.scanResultDesc = scanResultDesc;
    }

    public String getFailMessage() {
        return failMessage;
    }

    public void setFailMessage(String failMessage) {
        this.failMessage = failMessage;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}
