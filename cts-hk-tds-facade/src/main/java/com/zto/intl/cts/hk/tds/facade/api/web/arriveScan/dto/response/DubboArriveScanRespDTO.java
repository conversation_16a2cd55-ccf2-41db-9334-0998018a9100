package com.zto.intl.cts.hk.tds.facade.api.web.arriveScan.dto.response;

import com.zto.intl.cts.hk.core.domain.web.ErrorInfo;

import java.io.Serializable;
import java.util.List;

/**
 * 到货扫描结果
 * <AUTHOR>
 * @date 2025/07/24
 */
public class DubboArriveScanRespDTO implements Serializable {

    private static final long serialVersionUID = 2351021192345234530L;
    /**
     * 成功数
     */
    private Integer successCount;

    /**
     * 失败数
     */
    private Integer failCount;

    /**
     * 运单件数 - 单个扫描返回
     */
    private Integer billCount;

    /**
     * 错误信息
     */
    private List<ErrorInfo> errors;

    public Integer getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(Integer successCount) {
        this.successCount = successCount;
    }

    public Integer getFailCount() {
        return failCount;
    }

    public void setFailCount(Integer failCount) {
        this.failCount = failCount;
    }

    public Integer getBillCount() {
        return billCount;
    }

    public void setBillCount(Integer billCount) {
        this.billCount = billCount;
    }

    public List<ErrorInfo> getErrors() {
        return errors;
    }

    public void setErrors(List<ErrorInfo> errors) {
        this.errors = errors;
    }
}
