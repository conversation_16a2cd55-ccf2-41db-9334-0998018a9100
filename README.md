一、核心对象类型与命名规范
对象类型	                         命名后缀/前缀	                               用途说明	                             示例
Entity	                              实体名 或 实体名Entity	                     与数据库表严格映射的模型,类名与数据库表名保持一致	          User 或 UserEntity
VO (View Object)	                 实体名VO	                                   前端展示数据对象（可能聚合多个实体字段）	UserVO
DTO (Data Transfer Object)	         实体名DTO	                                 服务间传输的数据对象（如RPC调用）,避免包含业务逻辑,可继承自Entity但应简化字段	UserDTO
ReqVO (Request VO)	                操作名ReqVO	                             接收前端请求参数的对象（通常对应API入参）	UserCreateReqVO
RespVO (Response VO)	操作名RespVO	返回给前端的响应对象	UserQueryRespVO

1. 请求和响应对象只包含必要的字段，不允许直接继承entity
2. 各功能请求响应一般独立创建，不允许混用


二、分层架构各层职责划分
## 项目分层和依赖如下
#### 注意：严格遵循此分层架构，避免嵌套依赖

######

```

                                   
                   provider
                      |      
                      |  
            _ _ _ _service          adapter  _ _ _ _
           |          |              |              |
          dao         |  _  _ _ _ _  |          外部API
                            |
                          commons          facade                    
                            |                |              
                            | _  _ _ _ _ _ _ |  
                                     |
                                   core
                                     |
                           github pagehelper      
            

```
1. web层：http统一入口，认证，鉴权，参数校验；
2. facade层：对外服务门面。包含Dubbo服务接口，请求/响应DTO，异常类枚举
           禁止包含：业务逻辑，不必要的第三方库依赖
3. adapter层：跨系统整合：对接外部服务（如支付、短信API）；隔离外部变化对核心业务的影响       
4. service层：负责本业务域的核心业务逻辑规则，定义事务边界。
5. provider层：adapter和service的调度和桥梁，
5. dao层：隐藏底层存储细节, 不处理事务，仅提供原子操作。


测试环境dubbo标签路由：服务提供方和调用方设置tag, 就可消费到指定实例。 例如启动参数添加-Dtitans.dubbo.tag=test

三、异常处理
统一响应体：所有接口返回标准格式
-- dubbo服务提供接口分两类：
业务异常：直接throw, 过滤器会自动封装或将自定义异常响应给调用方；
成功响应：
提供给web端，直接响应数据对象，不用封装BaseResponse
提供给其他项目系统（不共享BizException），封装BaseResponse


异常分类：区分业务异常和系统异常

全局过滤器：服务端和消费者端分别处理
断言风格处理业务异常
错误码规范：明确定义错误码体系







            
           
