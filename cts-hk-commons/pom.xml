<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cts-hk</artifactId>
        <groupId>com.zto</groupId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cts-hk-commons</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.zto</groupId>
            <artifactId>cts-hk-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zto.intl</groupId>
            <artifactId>zto-international-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
        </dependency>

        <dependency>
            <groupId>ma.glasnost.orika</groupId>
            <artifactId>orika-core</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.25</version>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <name>libs-release</name>
            <url>https://jfrog.dev.ztosys.com/artifactory/libs-release/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>libs-snapshot</name>
            <url>https://jfrog.dev.ztosys.com/artifactory/libs-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
