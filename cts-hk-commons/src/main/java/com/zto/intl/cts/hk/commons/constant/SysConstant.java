package com.zto.intl.cts.hk.commons.constant;

/**
 * 系统常量定义
 * <AUTHOR>
 * @date 2025/7/28 11:05
 */
public class SysConstant {
    /** 成功描述 **/
    public static final String SUCCESS_DESC = "成功";
    /** 失败描述 **/
    public static final String FAIL_DESC = "失败";
    /** 是 描述 **/
    public static final String YES_DESC = "是";
    /** 否 描述 **/
    public static final String NO_DESC = "否";

    /** Y **/
    public static final String YES = "Y";
    /** N **/
    public static final String NO = "N";
    /** zsmp **/
    public static final String ZSMP_DOMAIN = "香港统一操作台";
    /** 订单来源 PLTS **/
    public static final String ORDER_SOURCE_PLTS = "PLTS";

    /** 系统 **/
    public static final String SYSTEM = "system";
    /** 任务最大执行次数 **/
    public static final Integer TASK_MAX_PUSH_COUNT = 6;

}
