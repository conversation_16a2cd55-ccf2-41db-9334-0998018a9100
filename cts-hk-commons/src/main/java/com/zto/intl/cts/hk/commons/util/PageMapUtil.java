package com.zto.intl.cts.hk.commons.util;

import com.github.pagehelper.Page;
import com.zto.intl.cts.hk.core.domain.web.PageResult;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description page工具类
 * @Date 2025/8/1
 * @Version 1.0
 */
public class PageMapUtil {

    /**
     * 使用Class类型进行分页数据转换
     * @param page 分页数据
     * @param tClass 目标类型
     * @param <T> 目标类型
     * @param <E> 源类型
     * @return 转换后的分页结果
     */
    public static <T, E> PageResult<T> map(Page<E> page, Class<T> tClass){
        List<T> list = BeanUtil.mapAsList(page.getResult(), tClass);
        return PageResult.map(page, list);
    }


    /**
     * 使用函数式接口进行分页数据转换
     * @param page 分页数据
     * @param mapper 转换函数
     * @param <T> 目标类型
     * @param <E> 源类型
     * @return 转换后的分页结果
     */
    public static <T, E> PageResult<T> map(Page<E> page, Function<E, T> mapper) {
        List<T> list = page.getResult().stream()
                .map(mapper)
                .collect(Collectors.toList());
        return PageResult.map(page, list);
    }
}
