package com.zto.intl.cts.hk.commons.util;

import com.zto.intl.cts.hk.core.exception.BizException;
import com.zto.intl.cts.hk.core.exception.IError;

/**
 * <AUTHOR>
 * @Description 断言工具类
 * @Date 2025/7/29
 * @Version 1.0
 */
public class Asserts {

    public static void isTrue (Boolean value, IError error){
        if(!value){
            throw new BizException(error);
        }
    }


    public static <T> void isTrue (Boolean value, IError error, T data){
        if(!value){
            throw new BizException(error, data);
        }
    }

}
