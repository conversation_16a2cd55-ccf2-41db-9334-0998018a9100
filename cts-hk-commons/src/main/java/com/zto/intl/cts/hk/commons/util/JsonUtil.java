package com.zto.intl.cts.hk.commons.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;

import java.util.List;

/**
 * 统一使用json工具栏，方便修改替换底层实现
 * <AUTHOR>
 * @since 2022-07-14 14JSo */
public class JsonUtil {

    public static String toJsonString(Object object) {
        return JSON.toJSONString(object);
    }

    public static <T> T parseObject(String jsonStr, Class<T> targetClass) {
        return JSON.parseObject(jsonStr, targetClass);
    }

    public static <T> T parseObject(String jsonStr, TypeReference<T> typeReference) {
        return JSON.parseObject(jsonStr, typeReference);
    }

    public static <T> List<T> parseArray(String jsonStr, Class<T> targetClass) {
        return JSON.parseArray(jsonStr, targetClass);
    }

}
