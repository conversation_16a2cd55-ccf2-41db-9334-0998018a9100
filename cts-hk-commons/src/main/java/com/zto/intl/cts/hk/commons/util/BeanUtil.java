package com.zto.intl.cts.hk.commons.util;

import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 对象工具类
 * <AUTHOR>
 * @date 20225/7/24
 */
public class BeanUtil {

    private BeanUtil() {
    }

    /**
     * 对象拷贝工具，字段名称不一致的需要手工处理
     *
     * @param source           源对象
     * @param target           目标对象
     * @param ignoreProperties 忽略字段
     * @date 2022/11/30 10:07
     */
    public static <T> T copyProperties(Object source, T target, String... ignoreProperties) {
        if (null == source || null == target) {
            return null;
        }
        BeanUtils.copyProperties(source, target, ignoreProperties);
        return target;
    }

    /**
     * 对象拷贝工具（帮助创建目标对象），字段名称不一致的需要手工处理
     *
     * @param source           源对象
     * @param targetClass      目标对象
     * @param ignoreProperties 忽略字段
     * @return T 目标对象
     * @date 2022/11/30 10:07
     */
    public static <S, T> T mapProperties(S source, Class<T> targetClass, String... ignoreProperties) {
        T target = BeanUtils.instantiateClass(targetClass);
        copyProperties(source, target, ignoreProperties);
        return target;
    }

    /**
     * 集合对象的转换，集合元素字段名称不一致的需要手工处理
     *
     * @param source 源集合
     * @param clazz  目标集合中元素类型
     * @return java.util.List<T> 目标集合
     * @date 2022/11/30 10:09
     */
    public static <E, T> List<T> mapAsList(List<E> source, Class<T> clazz) {
        if (null == source) {
            return new ArrayList<T>();
        }
        return source
                .stream()
                .map(s -> {
                    T t = BeanUtils.instantiateClass(clazz);
                    BeanUtils.copyProperties(s, t);
                    return t;
                })
                .collect(Collectors.toList());
    }

    /**
     * 集合中元素的自定义转换
     *
     * @param source   源集合
     * @param function 元素转换方法
     * @return java.util.List<T>
     * @date 2022/11/30 10:11
     */
    public static <E, T> List<T> mapAsList(List<E> source, Function<E, T> function) {
        return source.stream().map(function).collect(Collectors.toList());
    }
}
