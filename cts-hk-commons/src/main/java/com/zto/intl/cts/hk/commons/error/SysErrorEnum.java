package com.zto.intl.cts.hk.commons.error;

import com.zto.intl.cts.hk.core.exception.IError;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 系统级别错误码
 */
public class SysErrorEnum {

    /**
     * 系统级别错误码
     * 10-99
     */
    @Getter
    @AllArgsConstructor
    public enum SysError implements IError {

        ACTION_OVERRIDE(10, "导出失败"),
        CHECK_ERROR(11, "校验失败"),
        ;

        private final Integer code;
        private final String message;
    }

}
