package com.zto.intl.cts.hk.commons.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.google.common.collect.Maps;
import com.zto.intl.cts.hk.commons.annotation.ExcelName;
import com.zto.intl.cts.hk.core.domain.web.PageQuery;
import com.zto.intl.cts.hk.core.domain.web.PageResult;
import com.zto.intl.cts.hk.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * Excel导出工具类
 *
 * <AUTHOR>
 * @version 2.0
 */
@Slf4j
public class EasyExcelUtils {

    /** 默认分页大小 */
    private static final int DEFAULT_PAGE_SIZE = 1500;

    /** 默认导出最大条数限制 */
    private static final int DEFAULT_MAX_EXPORT_LIMIT = 100_000;

    /** Excel文件扩展名 */
    private static final String EXCEL_EXTENSION = ExcelTypeEnum.XLSX.getValue();

    /** 默认文件名 */
    private static final String DEFAULT_FILE_NAME = "导出文件";


    private EasyExcelUtils() {
        throw new UnsupportedOperationException("utility class...");
    }

    // ==================== 导出单页Excel ====================

    /**
     * 导出单页Excel
     */
    public static <T> void exportSingleSheet(HttpServletResponse response,
                                             Function<PageQuery<?>, PageResult<?>> function,
                                             PageQuery<?> pageQuery,
                                             Class<T> excelClass) throws IOException {
        exportToResponse(response, function, pageQuery, excelClass);
    }

    /**
     * 导出单页Excel
     */
    public static <T> void exportSingleSheet(HttpServletResponse response,
                                             Function<PageQuery<?>, PageResult<?>> function,
                                             PageQuery<?> pageQuery,
                                             Class<T> excelClass,
                                             List<String> includeColumns) throws IOException {
        exportToResponse(response, function, pageQuery, excelClass, null, includeColumns);
    }

    /**
     * 导出单页Excel到输出流
     */

    public static <T> void exportSingleSheet(OutputStream outputStream,
                                             Function<PageQuery<?>, PageResult<?>> function,
                                             PageQuery<?> pageQuery,
                                             Class<T> excelClass,
                                             List<String> includeColumns) throws IOException {
        exportToStream(outputStream, function, pageQuery, excelClass, includeColumns);
    }

    // ==================== 主要导出方法 ====================

    /**
     * 导出Excel到HTTP响应流（自动获取文件名）
     *
     * @param response HTTP响应对象
     * @param queryFunction 分页查询函数
     * @param pageQuery 分页查询参数
     * @param excelClass Excel模板类
     * @param <T> Excel模板类型
     */
    private static <T> void exportToResponse(HttpServletResponse response,
                                             Function<PageQuery<?>, PageResult<?>> queryFunction,
                                             PageQuery<?> pageQuery,
                                             Class<T> excelClass) throws IOException {
        exportToResponse(response, queryFunction, pageQuery, excelClass, null, null);
    }

    /**
     * 导出Excel到HTTP响应流（指定文件名）
     *
     * @param response HTTP响应对象
     * @param queryFunction 分页查询函数
     * @param pageQuery 分页查询参数
     * @param excelClass Excel模板类
     * @param fileName 文件名（不含扩展名）
     * @param <T> Excel模板类型
     */
    private static <T> void exportToResponse(HttpServletResponse response,
                                             Function<PageQuery<?>, PageResult<?>> queryFunction,
                                             PageQuery<?> pageQuery,
                                             Class<T> excelClass,
                                             String fileName) throws IOException {
        exportToResponse(response, queryFunction, pageQuery, excelClass, fileName, null);
    }

    /**
     * 导出Excel到HTTP响应流（完整参数）
     *
     * @param response HTTP响应对象
     * @param queryFunction 分页查询函数
     * @param pageQuery 分页查询参数
     * @param excelClass Excel模板类
     * @param fileName 文件名（不含扩展名）
     * @param includeColumns 指定导出的字段列表
     * @param <T> Excel模板类型
     */
    private static <T> void exportToResponse(HttpServletResponse response,
                                             Function<PageQuery<?>, PageResult<?>> queryFunction,
                                             PageQuery<?> pageQuery,
                                             Class<T> excelClass,
                                             String fileName,
                                             List<String> includeColumns) throws IOException {
        // 设置响应头
        setResponseHeaders(response, fileName, excelClass);

        // 导出到输出流
        exportToStream(response.getOutputStream(), queryFunction, pageQuery, excelClass, includeColumns);
    }


    /**
     * 导出Excel到输出流
     *
     * @param outputStream 输出流
     * @param queryFunction 分页查询函数
     * @param pageQuery 分页查询参数
     * @param excelClass Excel模板类
     * @param includeColumns 指定导出的字段列表
     * @param <T> Excel模板类型
     */
    private static <T> void exportToStream(OutputStream outputStream,
                                           Function<PageQuery<?>, PageResult<?>> queryFunction,
                                           PageQuery<?> pageQuery,
                                           Class<T> excelClass,
                                           List<String> includeColumns) throws IOException {
        try {
            // 参数校验
            validateExportParams(queryFunction, pageQuery, excelClass);

            // 执行导出
            doExportToStream(outputStream, queryFunction, pageQuery, excelClass, includeColumns);

        } catch (Exception e) {
            log.error("Excel导出失败", e);
            throw new IOException("Excel导出失败: " + e.getMessage(), e);
        }
    }


    // ==================== 核心实现方法 ====================

    /**
     * 设置HTTP响应头
     */
    private static <T> void setResponseHeaders(HttpServletResponse response, String fileName, Class<T> excelClass) throws UnsupportedEncodingException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");

        // 获取文件名
        String finalFileName = getFileName(fileName, excelClass);

        // 设置文件名
        String encodedFileName = URLEncoder.encode(finalFileName, String.valueOf(StandardCharsets.UTF_8));
        response.setHeader("Content-Disposition", "attachment; filename*=utf-8''" + encodedFileName + EXCEL_EXTENSION);
    }

    /**
     * 获取文件名
     */
    private static <T> String getFileName(String fileName, Class<T> excelClass) {
        if (StringUtils.isNotBlank(fileName)) {
            return fileName;
        }

        // 尝试从注解获取文件名
        ExcelName annotation = excelClass.getAnnotation(ExcelName.class);
        if (annotation != null && StringUtils.isNotBlank(annotation.value())) {
            return annotation.value();
        }

        return DEFAULT_FILE_NAME;
    }


    /**
     * 参数校验
     */
    private static <T> void validateExportParams(Function<PageQuery<?>, PageResult<?>> queryFunction,
                                                 PageQuery<?> pageQuery,
                                                 Class<T> excelClass) {
        if (queryFunction == null) {
            throw new IllegalArgumentException("查询函数不能为空");
        }
        if (pageQuery == null) {
            throw new IllegalArgumentException("分页查询参数不能为空");
        }
        if (excelClass == null) {
            throw new IllegalArgumentException("Excel模板类不能为空");
        }
    }

    /**
     * 执行导出到输出流
     */
    private static <T> void doExportToStream(OutputStream outputStream,
                                             Function<PageQuery<?>, PageResult<?>> queryFunction,
                                             PageQuery<?> pageQuery,
                                             Class<T> excelClass,
                                             List<String> includeColumns) throws IOException {

        try (ExcelWriter excelWriter = EasyExcel.write(outputStream, excelClass)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .build()) {

            // 创建写入Sheet
            ExcelWriterSheetBuilder sheetBuilder = EasyExcel.writerSheet()
                    .head(excelClass)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(new AutoHeadColumnWidthStyleStrategy());

            // 设置指定导出的字段
            if (CollectionUtils.isNotEmpty(includeColumns)) {
                sheetBuilder.includeColumnFieldNames(includeColumns);
            }

            WriteSheet writeSheet = sheetBuilder.build();

            // 设置分页参数
            PageQuery<?> queryParam = PageQuery.of(pageQuery.getPageNum(), DEFAULT_PAGE_SIZE, pageQuery.getCondition());

            // 分页查询并写入数据
            writeDataByPages(excelWriter, writeSheet, queryFunction, queryParam, excelClass);
        }
    }

    /**
     * 分页写入数据
     */
    private static <T> void writeDataByPages(ExcelWriter excelWriter,
                                             WriteSheet writeSheet,
                                             Function<PageQuery<?>, PageResult<?>> queryFunction,
                                             PageQuery<?> queryParam,
                                             Class<T> excelClass) {
        int pageNum = 0;
        PageResult<?> pageData;

        do {
            pageNum++;
            queryParam.setPageNum(pageNum);
            pageData = queryFunction.apply(queryParam);

            // 第一页时检查总数限制
            if (pageNum == 1 && pageData.getTotal() > DEFAULT_MAX_EXPORT_LIMIT) {
                throw new BizException(515, "导出数据量过大，最大支持" + DEFAULT_MAX_EXPORT_LIMIT + "条记录");
            }

            // 写入数据
            if (CollectionUtils.isNotEmpty(pageData.getData())) {
                List<T> convertedData = convertData(pageData.getData(), excelClass);
                excelWriter.write(convertedData, writeSheet);
            }

        } while (CollectionUtils.isNotEmpty(pageData.getData()) &&
                pageData.getData().size() >= queryParam.getPageSize());
    }

    /**
     * 转换数据
     */
    @SuppressWarnings("unchecked")
    private static <T> List<T> convertData(List<?> sourceData, Class<T> targetClass) {
        if (CollectionUtils.isEmpty(sourceData)) {
            return new ArrayList<>();
        }

        // 如果源数据类型与目标类型相同，直接返回
        if (!sourceData.isEmpty() && targetClass.isInstance(sourceData.get(0))) {
            return (List<T>) sourceData;
        }

        // 使用BeanUtil进行转换
        return BeanUtil.mapAsList(sourceData, targetClass);
    }

    // ==================== 工具方法 ====================

    /**
     * 获取Excel表头映射
     *
     * @param clazz Excel类
     * @return 表头映射 (索引 -> 表头名称)
     */
    public static Map<Integer, String> getHeaderMap(Class<?> clazz) {
        Map<Integer, String> result = Maps.newHashMap();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);
            ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);

            if (excelProperty != null) {
                int index = excelProperty.index();
                String[] values = excelProperty.value();
                StringBuilder value = new StringBuilder();
                for (String v : values) {
                    value.append(v);
                }
                result.put(index, value.toString());
            }
        }

        return result;
    }

    /**
     * 导出简单数据列表到响应流
     *
     * @param response HTTP响应对象
     * @param dataList 数据列表
     * @param excelClass Excel模板类
     * @param fileName 文件名（不含扩展名）
     * @param <T> Excel模板类型
     */
    public static <T> void exportListToResponse(HttpServletResponse response,
                                                List<T> dataList,
                                                Class<T> excelClass,
                                                String fileName) throws IOException {
        // 设置响应头
        setResponseHeaders(response, fileName, excelClass);

        // 导出到输出流
        exportListToStream(response.getOutputStream(), dataList, excelClass);
    }

    /**
     * 导出简单数据列表到输出流
     *
     * @param outputStream 输出流
     * @param dataList 数据列表
     * @param excelClass Excel模板类
     * @param <T> Excel模板类型
     */
    public static <T> void exportListToStream(OutputStream outputStream,
                                              List<T> dataList,
                                              Class<T> excelClass) throws IOException {
        try {
            if (excelClass == null) {
                throw new IllegalArgumentException("Excel模板类不能为空");
            }

            // 检查数据量限制
            if (CollectionUtils.isNotEmpty(dataList) && dataList.size() > DEFAULT_MAX_EXPORT_LIMIT) {
                throw new BizException(515, "导出数据量过大，最大支持" + DEFAULT_MAX_EXPORT_LIMIT + "条记录");
            }

            EasyExcel.write(outputStream, excelClass)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("数据")
                    .doWrite(dataList);

        } catch (Exception e) {
            log.error("Excel导出失败", e);
            throw new IOException("Excel导出失败: " + e.getMessage(), e);
        }
    }
}
