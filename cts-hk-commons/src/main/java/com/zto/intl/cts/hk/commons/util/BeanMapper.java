package com.zto.intl.cts.hk.commons.util;

import lombok.extern.slf4j.Slf4j;
import ma.glasnost.orika.MapperFacade;
import ma.glasnost.orika.MapperFactory;
import ma.glasnost.orika.MappingContext;
import ma.glasnost.orika.converter.BidirectionalConverter;
import ma.glasnost.orika.impl.DefaultMapperFactory;
import ma.glasnost.orika.metadata.Type;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import java.math.BigDecimal;

/**
 * 数据实例映射器
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public final class BeanMapper {

    private static final class InstanceHolder {
        private static final MapperFacade INSTANCE;
        static {
            MapperFactory mapperFactory = new DefaultMapperFactory.Builder().build();
            mapperFactory.getConverterFactory().registerConverter(new ByteIntegerConverter());
            mapperFactory.getConverterFactory().registerConverter(new BigDecimalIntegerConverter());
            mapperFactory.getConverterFactory().registerConverter(new StringByteConverter());
            mapperFactory.getConverterFactory().registerConverter(new StringShortConverter());
            mapperFactory.getConverterFactory().registerConverter(new StringIntegerConverter());
            mapperFactory.getConverterFactory().registerConverter(new StringLongConverter());
            INSTANCE = mapperFactory.getMapperFacade();
        }
    }


    public static MapperFacade getInstance() {
        return InstanceHolder.INSTANCE;
    }


    /** Byte <-> Integer转换器 */
    private static final class ByteIntegerConverter extends BidirectionalConverter<Byte, Integer> {

        @Override
        public Integer convertTo(Byte source, Type<Integer> destinationType, MappingContext mappingContext) {
            return Integer.valueOf(source);
        }

        @Override
        public Byte convertFrom(Integer source, Type<Byte> destinationType, MappingContext mappingContext) {
            return Byte.valueOf(String.valueOf(source));
        }
    }


    /** BigDecimal <-> Integer转换器 */
    private static final class BigDecimalIntegerConverter extends BidirectionalConverter<BigDecimal, Integer> {

        @Override
        public Integer convertTo(BigDecimal source, Type<Integer> destinationType, MappingContext mappingContext) {
            log.warn("此转换有可能造成精度丢失，请检查是否必须这样处理!!!,调用链:{}",
                ExceptionUtils.getStackTrace(new RuntimeException("此转换有可能造成精度丢失，请检查是否必须这样处理!!!")));

            return source.intValue();
        }

        @Override
        public BigDecimal convertFrom(Integer source, Type<BigDecimal> destinationType, MappingContext mappingContext) {
            return new BigDecimal(source);
        }
    }


    /** String <-> Byte转换器 */
    private static final class StringByteConverter extends BidirectionalConverter<String, Byte> {

        @Override
        public Byte convertTo(String source, Type<Byte> destinationType, MappingContext mappingContext) {
            if (StringUtils.isNotBlank(source)) {
                return Byte.valueOf(source);
            }
            else {
                log.warn("空字符串转换为数值，请检查是否必须这样处理!!!,调用链:{}",
                    ExceptionUtils.getStackTrace(new RuntimeException("空字符串转换为数值，请检查是否必须这样处理!!!")));
                return null;
            }
        }

        @Override
        public String convertFrom(Byte source, Type<String> destinationType, MappingContext mappingContext) {
            if (source == null) {
                return null;
            }
            else {
                return source.toString();
            }
        }
    }


    /** String <-> Short转换器 */
    private static final class StringShortConverter extends BidirectionalConverter<String, Short> {

        @Override
        public Short convertTo(String source, Type<Short> destinationType, MappingContext mappingContext) {
            if (StringUtils.isNotBlank(source)) {
                return Short.valueOf(source);
            }
            else {
                log.warn("空字符串转换为数值，请检查是否必须这样处理!!!,调用链:{}",
                    ExceptionUtils.getStackTrace(new RuntimeException("空字符串转换为数值，请检查是否必须这样处理!!!")));
                return null;
            }
        }

        @Override
        public String convertFrom(Short source, Type<String> destinationType, MappingContext mappingContext) {
            if (source == null) {
                return null;
            }
            else {
                return source.toString();
            }
        }
    }


    /** String <-> Integer转换器 */
    private static final class StringIntegerConverter extends BidirectionalConverter<String, Integer> {

        @Override
        public Integer convertTo(String source, Type<Integer> destinationType, MappingContext mappingContext) {
            if (StringUtils.isNotBlank(source)) {
                return Integer.valueOf(source);
            }
            else {
                log.warn("空字符串转换为数值，请检查是否必须这样处理!!!,调用链:{}",
                    ExceptionUtils.getStackTrace(new RuntimeException("空字符串转换为数值，请检查是否必须这样处理!!!")));
                return null;
            }
        }

        @Override
        public String convertFrom(Integer source, Type<String> destinationType, MappingContext mappingContext) {
            if (source == null) {
                return null;
            }
            else {
                return source.toString();
            }
        }
    }


    /** String <-> Long转换器 */
    private static final class StringLongConverter extends BidirectionalConverter<String, Long> {

        @Override
        public Long convertTo(String source, Type<Long> destinationType, MappingContext mappingContext) {
            if (StringUtils.isNotBlank(source)) {
                return Long.valueOf(source);
            }
            else {
                log.warn("空字符串转换为数值，请检查是否必须这样处理!!!,调用链:{}",
                    ExceptionUtils.getStackTrace(new RuntimeException("空字符串转换为数值，请检查是否必须这样处理!!!")));
                return null;
            }
        }

        @Override
        public String convertFrom(Long source, Type<String> destinationType, MappingContext mappingContext) {
            if (source == null) {
                return null;
            }
            else {
                return source.toString();
            }
        }
    }
}
