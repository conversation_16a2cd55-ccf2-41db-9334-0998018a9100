package com.zto.intl.cts.hk.commons.util;

import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Excel表头自适应列宽策略
 *
 * <p>功能特性：</p>
 * <ul>
 *   <li>自动计算表头和数据的最佳列宽</li>
 *   <li>支持中文字符宽度计算</li>
 *   <li>设置最小和最大列宽限制</li>
 *   <li>线程安全的宽度缓存</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0
 */
@Slf4j
public class AutoHeadColumnWidthStyleStrategy extends AbstractColumnWidthStyleStrategy {

    /** Excel列宽最大值（Excel限制） */
    private static final int MAX_COLUMN_WIDTH = 255;

    /** 最小列宽（保底宽度） */
    private static final int MIN_COLUMN_WIDTH = 10;

    /** 默认列宽 */
    private static final int DEFAULT_COLUMN_WIDTH = 20;

    /** 小列宽补偿值 */
    private static final int SMALL_WIDTH_COMPENSATION = 3;

    /** Excel列宽转换系数 */
    private static final int EXCEL_WIDTH_FACTOR = 256;

    /** 中文字符宽度系数 */
    private static final double CHINESE_CHAR_WIDTH_FACTOR = 1.5;

    /** 列宽缓存 (SheetNo -> (ColumnIndex -> MaxWidth)) */
    private final Map<Integer, Map<Integer, Integer>> columnWidthCache = new ConcurrentHashMap<>();

    /**
     * 默认构造函数
     */
    public AutoHeadColumnWidthStyleStrategy() {
        // 默认构造函数
    }

    @Override
    protected void setColumnWidth(WriteSheetHolder writeSheetHolder,
                                 List<WriteCellData<?>> cellDataList,
                                 Cell cell,
                                 Head head,
                                 Integer relativeRowIndex,
                                 Boolean isHead) {
        try {
            // 只处理表头或有数据的单元格
            if (!shouldSetWidth(isHead, cellDataList)) {
                return;
            }

            // 获取或创建当前Sheet的列宽缓存
            Map<Integer, Integer> sheetColumnWidthMap = getSheetColumnWidthMap(writeSheetHolder.getSheetNo());

            // 计算当前单元格的宽度
            Integer cellWidth = calculateCellWidth(cellDataList, cell, isHead);
            if (cellWidth <= 0) {
                return;
            }

            // 应用宽度限制和补偿
            Integer adjustedWidth = adjustColumnWidth(cellWidth);

            // 更新列宽（如果当前宽度更大）
            updateColumnWidth(writeSheetHolder, cell.getColumnIndex(), adjustedWidth, sheetColumnWidthMap);

        } catch (Exception e) {
            log.warn("设置列宽时发生异常，列索引: {}, 错误: {}", cell.getColumnIndex(), e.getMessage());
            // 发生异常时设置默认宽度
            setDefaultColumnWidth(writeSheetHolder, cell.getColumnIndex());
        }
    }

    /**
     * 判断是否需要设置列宽
     */
    private boolean shouldSetWidth(Boolean isHead, List<WriteCellData<?>> cellDataList) {
        return isHead || CollectionUtils.isNotEmpty(cellDataList);
    }

    /**
     * 获取Sheet的列宽缓存
     */
    private Map<Integer, Integer> getSheetColumnWidthMap(Integer sheetNo) {
        return columnWidthCache.computeIfAbsent(sheetNo, k -> new ConcurrentHashMap<>());
    }

    /**
     * 计算单元格宽度
     */
    private Integer calculateCellWidth(List<WriteCellData<?>> cellDataList, Cell cell, Boolean isHead) {
        if (isHead) {
            return calculateHeaderWidth(cell);
        } else {
            return calculateDataWidth(cellDataList);
        }
    }

    /**
     * 计算表头宽度
     */
    private Integer calculateHeaderWidth(Cell cell) {
        try {
            String headerValue = cell.getStringCellValue();
            if (StringUtils.isBlank(headerValue)) {
                return MIN_COLUMN_WIDTH;
            }
            return calculateStringWidth(headerValue);
        } catch (Exception e) {
            log.debug("计算表头宽度失败: {}", e.getMessage());
            return DEFAULT_COLUMN_WIDTH;
        }
    }

    /**
     * 计算数据宽度
     */
    private Integer calculateDataWidth(List<WriteCellData<?>> cellDataList) {
        if (CollectionUtils.isEmpty(cellDataList)) {
            return MIN_COLUMN_WIDTH;
        }

        WriteCellData<?> cellData = cellDataList.get(0);
        CellDataTypeEnum type = cellData.getType();

        if (type == null) {
            return MIN_COLUMN_WIDTH;
        }

        try {
            switch (type) {
                case STRING:
                    String stringValue = cellData.getStringValue();
                    return StringUtils.isNotBlank(stringValue) ?
                           calculateStringWidth(stringValue) : MIN_COLUMN_WIDTH;

                case BOOLEAN:
                    return calculateStringWidth(cellData.getBooleanValue().toString());

                case NUMBER:
                    return calculateStringWidth(cellData.getNumberValue().toString());

                default:
                    return DEFAULT_COLUMN_WIDTH;
            }
        } catch (Exception e) {
            log.debug("计算数据宽度失败: {}", e.getMessage());
            return DEFAULT_COLUMN_WIDTH;
        }
    }

    /**
     * 计算字符串宽度（考虑中文字符）
     */
    private Integer calculateStringWidth(String text) {
        if (StringUtils.isBlank(text)) {
            return MIN_COLUMN_WIDTH;
        }

        // 使用UTF-8字节长度作为基础宽度
        byte[] bytes = text.getBytes(StandardCharsets.UTF_8);
        double width = bytes.length;

        // 对中文字符进行宽度补偿
        long chineseCharCount = text.chars()
                .filter(ch -> ch > 127)
                .count();

        if (chineseCharCount > 0) {
            width += chineseCharCount * CHINESE_CHAR_WIDTH_FACTOR;
        }

        return (int) Math.ceil(width);
    }

    /**
     * 调整列宽（应用限制和补偿）
     */
    private Integer adjustColumnWidth(Integer width) {
        // 应用最大宽度限制
        if (width > MAX_COLUMN_WIDTH) {
            return MAX_COLUMN_WIDTH;
        }

        // 应用最小宽度限制
        if (width < MIN_COLUMN_WIDTH) {
            return MIN_COLUMN_WIDTH;
        }

        // 对较小的宽度进行补偿
        if (width < DEFAULT_COLUMN_WIDTH) {
            return width + SMALL_WIDTH_COMPENSATION;
        }

        return width;
    }

    /**
     * 更新列宽
     */
    private void updateColumnWidth(WriteSheetHolder writeSheetHolder,
                                  Integer columnIndex,
                                  Integer newWidth,
                                  Map<Integer, Integer> sheetColumnWidthMap) {
        Integer currentMaxWidth = sheetColumnWidthMap.get(columnIndex);

        // 如果新宽度更大，则更新
        if (currentMaxWidth == null || newWidth > currentMaxWidth) {
            sheetColumnWidthMap.put(columnIndex, newWidth);
            writeSheetHolder.getSheet().setColumnWidth(columnIndex, newWidth * EXCEL_WIDTH_FACTOR);
        }
    }

    /**
     * 设置默认列宽
     */
    private void setDefaultColumnWidth(WriteSheetHolder writeSheetHolder, Integer columnIndex) {
        try {
            writeSheetHolder.getSheet().setColumnWidth(columnIndex, DEFAULT_COLUMN_WIDTH * EXCEL_WIDTH_FACTOR);
        } catch (Exception e) {
            log.warn("设置默认列宽失败，列索引: {}", columnIndex);
        }
    }

    /**
     * 清理缓存（可在导出完成后调用）
     */
    public void clearCache() {
        columnWidthCache.clear();
    }

    /**
     * 获取缓存大小（用于监控）
     */
    public int getCacheSize() {
        return columnWidthCache.size();
    }
}
