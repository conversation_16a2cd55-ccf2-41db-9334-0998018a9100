<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cts-hk</artifactId>
        <groupId>com.zto</groupId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cts-hk-web</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.zto</groupId>
            <artifactId>cts-hk-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zto</groupId>
            <artifactId>cts-hk-base-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zto</groupId>
            <artifactId>cts-hk-transfer-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zto</groupId>
            <artifactId>cts-hk-tds-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zto.titans</groupId>
            <artifactId>titans-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zto.titans</groupId>
            <artifactId>titans-dubbo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zto.zsmp</groupId>
            <artifactId>zsmp-annotation</artifactId>
        </dependency>

        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.zto.zsmp</groupId>
                <artifactId>zsmp-doc-maven-plugin</artifactId>
                <version>0.0.2-SNAPSHOT</version>
                <executions>
                    <execution>
                        <phase>install</phase>
                        <goals>
                            <goal>zsmpDoc</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <serverUrl>https://zsmp.dev.ztosys.com</serverUrl>
                    <token>cb17333fa2214de29c436ec94244d048</token>
                </configuration>
            </plugin>
        </plugins>
    </build>


</project>
