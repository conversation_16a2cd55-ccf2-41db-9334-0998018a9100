package com.zto.intl.cts.hk.web.tds.arriveScan.vo.response;

import com.zto.intl.cts.hk.core.domain.web.ErrorInfo;
import lombok.Data;

import java.util.List;

/**
 * 到货扫描结果
 * <AUTHOR>
 * @date 2025/07/24
 */
@Data
public class ArriveScanRespVO {

    /**
     * 成功数
     */
    private Integer successCount;

    /**
     * 失败数
     */
    private Integer failCount;

    /**
     * 运单件数 - 单个扫描返回
     */
    private Integer billCount;

    /**
     * 扫描错误信息
     */
    private List<ErrorInfo> errors;
}
