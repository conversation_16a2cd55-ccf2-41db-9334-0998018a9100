//package com.zto.intl.cts.hk.web.tds;
//
//import com.alibaba.dubbo.config.annotation.Reference;
//import com.zto.intl.common.response.ResponseEnvelope;
//import com.zto.intl.cts.hk.base.facade.api.web.DubboUserService;
//import com.zto.intl.cts.hk.web.WebResponse;
//import com.zto.titans.web.filter.User;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.security.core.Authentication;
//import org.springframework.security.core.context.SecurityContextHolder;
//import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.servlet.http.Cookie;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import javax.servlet.http.HttpSession;
//import java.util.Arrays;
//
///**
// * <AUTHOR>
// * @Description 测试controller
// * @Date 2025/7/7
// * @Version 1.0
// */
//@Slf4j
//@RequestMapping
//@RestController
//public class TestController {
//
////    @Reference
////    TdsDubboService tdsDubboService;
////    @Reference
////    TransferDubboService transferDubboService;
////    @Reference
////    PayDubboService payDubboService;
//    @Reference
//DubboUserService dubboUserService;
//
//    @RequestMapping("hello")
//    public WebResponse<String> test(HttpServletRequest request, HttpSession session){
//        // 获取请求中的Cookie值
//        String cookieSessionId = Arrays.stream(request.getCookies())
//                .filter(c -> "SESSION".equalsIgnoreCase(c.getName()))
//                .findFirst()
//                .map(Cookie::getValue)
//                .orElse("NO_SESSION_COOKIE");
//
//        // 获取服务器端Session ID
//        String serverSessionId = session.getId();
//
//        // 获取用户信息
//        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
//        String username = auth != null ? auth.getName() : "anonymous";
//
//
//        String test = dubboUserService.testWeb();
//
//        String msg = String.format(
//                "Cookie SESSION: %s | Server SessionID: %s | User: %s | %s | %s | %s",
//                cookieSessionId,
//                serverSessionId,
//                username,
//                test
//                );
//        log.info(msg);
//        return WebResponse.success(msg);
//    }
//
////    @RequestMapping("helloResponse")
////    public BaseResponse<String> helloResponse(){
////        return payDubboService.testResponse();
////    }
//
//
////    @RequestMapping("helloWeb")
////    public WebResponse<String> helloWeb(){
////        String data = tdsDubboService.test();
////        return WebResponse.success(data);
////    }
//
//
//    @RequestMapping("redirect")
//    public String redirect(){
//        User user = (User) SecurityContextHolder.getContext().getAuthentication();
//        return "login success" + user.getFullname() + "!  welcome to cts-hk!!!";
//    }
//
//
//    @RequestMapping("logout")
//    public ResponseEnvelope<String> logout(HttpServletRequest request, HttpServletResponse response){
//        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
//        if (authentication != null) {
//            new SecurityContextLogoutHandler().logout(request, response, authentication);
//        }
//        return ResponseEnvelope.of("bye bye");
//    }
//
//    @GetMapping("getUserInfo")
//    @PreAuthorize("@permissionService.hasPermission('user_manage')")
//    public ResponseEnvelope<User> getUserInfo() {
//        log.info("[getUserInfo]...");
//        Authentication auth = SecurityContextHolder.getContext()
//                .getAuthentication();
//        User user = null;
//        if (auth instanceof User) {
//            user = (User) auth;
//        }
//        return ResponseEnvelope.of(user);
//    }
//}
