package com.zto.intl.cts.hk.web.tds.outScan.vo.request;

import com.zto.intl.cts.hk.core.enums.ScanNoTypeEnum;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/24
 */
@Data
public class OutScanCheckReqVO {
    /**
     * 单号类型
     * 运单号: BILL_CODE,
     * 客户单号：CUSTOMER_ORDER_NO
     */
    @NotNull(message = "单号类型不能为空")
    private ScanNoTypeEnum noTypeEnum;

    /** 单号 **/
    @NotNull(message = "单号不能为空")
    private List<String> noList;
}
