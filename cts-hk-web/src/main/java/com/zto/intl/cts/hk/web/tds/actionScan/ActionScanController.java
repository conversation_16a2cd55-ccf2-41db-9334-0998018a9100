package com.zto.intl.cts.hk.web.tds.actionScan;

import com.alibaba.dubbo.config.annotation.Reference;
import com.zto.intl.cts.hk.commons.constant.SysConstant;
import com.zto.intl.cts.hk.commons.util.BeanUtil;
import com.zto.intl.cts.hk.core.domain.web.ErrorInfo;
import com.zto.intl.cts.hk.core.enums.ScanNoTypeEnum;
import com.zto.intl.cts.hk.core.domain.web.PageQuery;
import com.zto.intl.cts.hk.core.domain.web.PageResult;
import com.zto.intl.cts.hk.tds.facade.api.web.actionScan.DubboActionScanService;
import com.zto.intl.cts.hk.tds.facade.api.web.actionScan.dto.request.DubboActionScanReqDTO;
import com.zto.intl.cts.hk.tds.facade.api.web.actionScan.dto.response.DubboActionScanRespDTO;
import com.zto.intl.cts.hk.web.base.WebResponse;
import com.zto.intl.cts.hk.web.tds.actionScan.vo.request.ActionScanPageReqVO;
import com.zto.intl.cts.hk.web.tds.actionScan.vo.request.ActionScanReqVO;
import com.zto.intl.cts.hk.web.tds.actionScan.vo.response.ActionScanPageRespVO;
import com.zto.intl.cts.hk.web.tds.actionScan.vo.response.ActionScanRespVO;
import com.zto.zsmp.annotation.ZsmpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 状态扫描
 * <AUTHOR>
 * @Date 2025/7/30
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("actionScan")
@ZsmpService(businessDomain = SysConstant.ZSMP_DOMAIN, name = "状态扫描", group = "状态扫描")
public class ActionScanController {

    @Reference
    private DubboActionScanService actionScanService;

    /**
     * 状态扫描
     * <AUTHOR>
     * @Param
     * @Return
     * @Date 2025/7/30
     */
    @PostMapping("scan")
//    @PreAuthorize("@ctsPermissionService.hasAnyPermission('')")
    public WebResponse<ActionScanRespVO> scanBatch(@Valid @RequestBody ActionScanReqVO reqVO){

        DubboActionScanReqDTO dubboActionScanReqDTO = new DubboActionScanReqDTO();
        dubboActionScanReqDTO.setUserCode(SecurityContextHolder.getContext().getAuthentication().getName());

        if(Objects.equals(ScanNoTypeEnum.BILL_CODE, reqVO.getNoTypeEnum())){
            dubboActionScanReqDTO.setBillCodes(reqVO.getNoList());
        }

        if(Objects.equals(ScanNoTypeEnum.CUSTOMER_ORDER_NO, reqVO.getNoTypeEnum())){
            dubboActionScanReqDTO.setCustomerOrderNos(reqVO.getNoList());
        }

        if(CollectionUtils.isEmpty(reqVO.getNoList())){
            actionScanService.scan(dubboActionScanReqDTO);
            return WebResponse.success();
        }

        DubboActionScanRespDTO respDTO = actionScanService.scanBatch(dubboActionScanReqDTO);
        ActionScanRespVO actionScanRespVO = BeanUtil.copyProperties(respDTO, new ActionScanRespVO());
        List<ErrorInfo> errorRespVOS = respDTO.getFailMessage().keySet().stream()
                .map(e -> new ErrorInfo(respDTO.getFailMessage().get(e), e)).collect(Collectors.toList());
        actionScanRespVO.setErrors(errorRespVOS);
        return WebResponse.success(actionScanRespVO);
    }

    /**
     * 状态扫描分页查询
     * <AUTHOR>
     * @Param
     * @Return
     * @Date 2025/7/30
     */
    @PostMapping("page")
    public PageResult<ActionScanPageRespVO> page(PageQuery<ActionScanPageReqVO> reqVO){
        return null;
    }



}
