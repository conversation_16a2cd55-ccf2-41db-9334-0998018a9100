package com.zto.intl.cts.hk.web.tds.arriveScan.vo.request;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 20225/7/24
 */
@Data
public class ArriveScanQueryReqVO {

    /** 开始扫描时间 **/
    private Date startTime;

    /** 结束扫描时间 **/
    private Date endTime;

    /** 支付状态 0-未处理 1-支付成功 2-支付失败 3-超时关闭 4-取消关闭 **/
    private Byte payStatus;

    /** 扫描结果 1-成功 0-失败 **/
    private Boolean scanResult;
}
