package com.zto.intl.cts.hk.web.tds.outScan.vo.response;

import lombok.Data;

import java.util.Date;

/**
 * 出货扫描结果
 * <AUTHOR>
 * @date 2025/07/24
 */
@Data
public class OutScanQueryRespVO {

    /** 序列 **/
    private Integer seq;

    /** 扫描时间 **/
    private Date operateTime;

    /** 运单号 **/
    private String billCode;

    /** 客户单号 **/
    private String customerOrderNo;

    /** 扫描结果 **/
    private Boolean isSuccess;

    /** 扫描结果描述 **/
    private String scanResultDesc;

    /** 扫描失败原因 **/
    private String failMessage;

    /** 操作人 **/
    private String operator;

}
