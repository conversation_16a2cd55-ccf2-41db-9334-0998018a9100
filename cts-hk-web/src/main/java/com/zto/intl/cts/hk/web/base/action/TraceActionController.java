package com.zto.intl.cts.hk.web.base.action;

import com.zto.intl.cts.hk.base.facade.api.web.DubboTraceActionService;
import com.zto.intl.cts.hk.base.facade.api.web.dto.response.DubboTraceActionRespDTO;
import com.zto.intl.cts.hk.commons.constant.SysConstant;
import com.zto.intl.cts.hk.commons.util.BeanUtil;
import com.zto.intl.cts.hk.web.base.WebResponse;
import com.zto.intl.cts.hk.web.base.action.vo.request.TraceActionQueryReqVO;
import com.zto.intl.cts.hk.web.base.action.vo.response.TraceActionQueryRespVO;
import com.zto.zsmp.annotation.ZsmpService;
import jdk.nashorn.internal.ir.annotations.Reference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 操作状态
 * <AUTHOR>
 * @Date 2025/7/30
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("traceAction")
@ZsmpService(businessDomain = SysConstant.ZSMP_DOMAIN, name = "操作状态", group = "操作状态")
public class TraceActionController {

    @Reference
    DubboTraceActionService traceActionService;

    /**
     * 状态查询
     * <AUTHOR>
     * @Param
     * @Return
     * @Date 2025/7/30
     */
    @PostMapping("query")
//    @PreAuthorize("@ctsPermissionService.hasAnyPermission('')")
    public WebResponse<List<TraceActionQueryRespVO>> scanBatch(@RequestBody TraceActionQueryReqVO reqVO){
        List<DubboTraceActionRespDTO> dubboTraceActionRespDTOS = traceActionService.likeByCodeOrName(reqVO.getKeyWord());
        List<TraceActionQueryRespVO> vos = BeanUtil.mapAsList(dubboTraceActionRespDTOS, TraceActionQueryRespVO.class);
        return WebResponse.success(vos);
    }

}
