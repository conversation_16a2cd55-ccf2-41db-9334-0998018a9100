package com.zto.intl.cts.hk.web.base;

import com.zto.intl.cts.hk.core.exception.IError;

import java.io.Serializable;

/**
 * Web响应统一封装类
 *
 * @param <T> 响应数据类型
 * <AUTHOR>
 * @version 2.0
 * @date 2025/7/25
 */
public class WebResponse<T> implements Serializable {
    private static final long serialVersionUID = -1059825926335653835L;

    private T data;
    private Boolean success;
    private BaseError error;

    /**
     * 默认构造函数
     */
    public WebResponse() {
        // 默认构造函数
    }

    /**
     * 成功响应构造函数
     *
     * @param data 响应数据
     */
    public WebResponse(T data) {
        this.data = data;
        this.success = true;
    }

    /**
     * 创建成功响应（无数据）
     *
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> WebResponse<T> success() {
        WebResponse<T> webResponse = new WebResponse<>();
        webResponse.setSuccess(true);
        return webResponse;
    }

    /**
     * 创建成功响应（带数据）
     *
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> WebResponse<T> success(T data) {
        return new WebResponse<>(data);
    }


    /**
     * 创建失败响应（无数据）
     *
     * @param errorCode 错误码
     * @param errorMsg 错误信息
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> WebResponse<T> fail(int errorCode, String errorMsg) {
        WebResponse<T> webResponse = new WebResponse<>();
        webResponse.error = new BaseError(errorCode, errorMsg);
        webResponse.success = false;
        return webResponse;
    }

    /**
     * 创建失败响应（带数据）
     *
     * @param error 错误信息接口
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> WebResponse<T> fail(IError error, T data) {
        WebResponse<T> webResponse = new WebResponse<>();
        webResponse.error = new BaseError(error.getCode(), error.getMessage());
        webResponse.data = data;
        webResponse.success = false;
        return webResponse;
    }

    /**
     * 创建失败响应（带数据）
     *
     * @param errorCode 错误码
     * @param errorMsg 错误信息
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> WebResponse<T> fail(int errorCode, String errorMsg, T data) {
        WebResponse<T> webResponse = new WebResponse<>();
        webResponse.error = new BaseError(errorCode, errorMsg);
        webResponse.data = data;
        webResponse.success = false;
        return webResponse;
    }

    /**
     * 创建失败响应（仅错误信息）
     *
     * @param error 错误信息接口
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> WebResponse<T> fail(IError error) {
        return fail(error.getCode(), error.getMessage());
    }

    // ==================== Getter and Setter ====================

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public BaseError getError() {
        return error;
    }

    public void setError(BaseError error) {
        this.error = error;
    }


    /**
     * 基础错误信息类
     */
    public static class BaseError implements Serializable {
        private static final long serialVersionUID = 1196345933197153607L;

        /** 错误码 */
        private int code;

        /** 错误信息 */
        private String message;

        /**
         * 默认构造函数
         */
        public BaseError() {
            // 默认构造函数
        }

        /**
         * 构造函数
         *
         * @param code 错误码
         * @param message 错误信息
         */
        public BaseError(int code, String message) {
            this.code = code;
            this.message = message;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }
}
