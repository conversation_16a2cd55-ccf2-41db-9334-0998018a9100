package com.zto.intl.cts.hk.web.tds.arriveScan.vo.response;

import com.zto.intl.cts.hk.core.domain.web.ErrorInfo;
import lombok.Data;

import java.util.List;

/**
 * 到货扫描校验响应VO
 * 
 * <AUTHOR>
 * @date 2025/8/1
 */
@Data
public class ArriveScanCheckRespVO {

    /**
     * 是否需要二次确认弹窗
     */
    private Boolean needConfirm = false;

    /**
     * 校验错误信息列表
     */
    private List<ErrorInfo> errors;
}
