package com.zto.intl.cts.hk.web.common;

import com.zto.titans.common.enums.ExceptionEnum;
import com.zto.titans.common.exception.ErrorCodeException;
import com.zto.titans.web.configuration.PermissionService;
import com.zto.titans.web.filter.User;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Component("ctsPermissionService")
public class CtsPermissionService extends PermissionService {

    public <T> T getAnyPermission(String key, Class<T> t) {
        User user = (User) SecurityContextHolder.getContext().getAuthentication();
        if (user == null) {
            return null;
        }

        List<String> asList = Arrays.asList(key.split(","));

        if (t.isAssignableFrom(Boolean.class)) {
            if (user.getPermissions() == null) {
                return (T) Boolean.valueOf(false);
            } else {
                return (T) Boolean.valueOf(CollectionUtils.containsAny(user.getPermissions(), asList));
            }

        } else if (t.isAssignableFrom(Integer.class)) {
            if (user.getIntPermissions() == null) {
                return null;
            } else {
                return (T) user.getIntPermissions().get(asList.get(0));
            }
        } else if (t.isAssignableFrom(String.class)) {
            if (user.getStringPermissions() == null) {
                return null;
            } else {
                return (T) user.getStringPermissions().get(asList.get(0));
            }
        } else if (t.isAssignableFrom(Set.class)) {
            if (user.getDataPermissions() == null) {
                return null;
            } else {
                return (T) user.getDataPermissions().get(asList.get(0));
            }
        } else {
            throw new ErrorCodeException(ExceptionEnum.PARAMETER_EXCEPTION, "权限获取", "未知的类型" + t + ",请选择[Boolean.class,String.class,Integer.class,Set.class]");
        }

    }

}
