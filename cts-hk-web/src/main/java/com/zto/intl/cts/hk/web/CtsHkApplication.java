package com.zto.intl.cts.hk.web;

import com.zto.intl.cts.hk.commons.util.SpringbootStartInit;
import com.zto.titans.common.annotation.EnableFramework;
import com.zto.titans.soa.dubbo.annotation.EnableDubbo;
import com.zto.titans.web.annotation.EnableWeb;
import com.zto.titans.web.session.SessionConst;
import org.springframework.boot.SpringApplication;

/**
 * <AUTHOR>
 * @Description web启动类
 * @Date 2025/7/12
 * @Version 1.0
 */
@EnableDubbo
@EnableFramework
@EnableWeb(sessionType = SessionConst.REDIS)
public class CtsHkApplication extends SpringbootStartInit {

    public static void main(String[] args) {

        init();
        SpringApplication.run(CtsHkApplication.class, args);
    }
}
