package com.zto.intl.cts.hk.web.filter;

import com.zto.intl.common.util.ExceptionUtils;
import com.zto.intl.cts.hk.core.exception.BizException;
import com.zto.intl.cts.hk.web.base.WebResponse;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description web统一异常处理
 * @Date 2025/7/27
 * @Version 1.0
 */
@RestControllerAdvice
public class WebGlobalExceptionHandler {


    // 处理业务异常
    @ExceptionHandler(BizException.class)
    public WebResponse<Object> handleBusinessException(BizException ex) {
        return WebResponse.fail(ex.getCode(),
                ex.getMessage(),
                ex.getData()
        );
    }

    // 处理参数校验异常
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public WebResponse<Void> handleValidationException(
            MethodArgumentNotValidException ex) {
        String errorMsg = ex.getBindingResult()
                .getFieldErrors()
                .stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining("; "));

        return WebResponse.fail(500, errorMsg);
    }

    // 兜底异常处理
    @ExceptionHandler(Exception.class)
    public WebResponse<Void> handleGeneralException(Exception ex) {

        return WebResponse.fail(500, ExceptionUtils.toShortString(ex, 100));
    }

}
