package com.zto.intl.cts.hk.web.tds.arriveScan.vo.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.zto.intl.cts.hk.commons.annotation.ExcelName;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 到货扫描导出Excel模板
 * 
 * <AUTHOR>
 * @date 2025/7/29
 */
@Data
@ExcelName("到货扫描记录")
public class ArriveScanExportVO {

    @ExcelProperty(value = "扫描时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date operateTime;

    @ExcelProperty(value = "运单号")
    private String billCode;

    @ExcelProperty(value = "客户单号")
    private String customerOrderNo;

    @ExcelProperty(value = "重量(kg)")
    private BigDecimal weight;

    @ExcelProperty(value = "长(cm)")
    private BigDecimal length;

    @ExcelProperty(value = "宽(cm)")
    private BigDecimal width;

    @ExcelProperty(value = "高(cm)")
    private BigDecimal height;

    @ExcelProperty(value = "支付状态")
    private String payStatusDesc;

    @ExcelProperty(value = "收件人姓名")
    private String consigneeName;

    @ExcelProperty(value = "收件人电话")
    private String consigneeMobile;

    @ExcelProperty(value = "收件人地址")
    private String consigneeAddress;

    @ExcelProperty(value = "派送方式")
    private String deliveryMethodDesc;

    @ExcelProperty(value = "扫描结果")
    private String scanResultDesc;

    @ExcelProperty(value = "失败原因")
    private String failMessage;

    @ExcelProperty(value = "操作人")
    private String operator;
}
