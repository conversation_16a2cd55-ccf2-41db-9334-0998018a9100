<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cts-hk-tds</artifactId>
        <groupId>com.zto</groupId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cts-hk-tds-adapter</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.zto</groupId>
            <artifactId>cts-hk-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zto</groupId>
            <artifactId>cts-hk-base-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.24</version>
        </dependency>
        <dependency>
            <groupId>com.zto.intl</groupId>
            <artifactId>intl-oms-overseas-interface</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zto.intl</groupId>
            <artifactId>intl-track-overseas-interface</artifactId>
        </dependency>


        <dependency>
            <groupId>com.zto.titans</groupId>
            <artifactId>titans-dubbo</artifactId>
        </dependency>
    </dependencies>

</project>
