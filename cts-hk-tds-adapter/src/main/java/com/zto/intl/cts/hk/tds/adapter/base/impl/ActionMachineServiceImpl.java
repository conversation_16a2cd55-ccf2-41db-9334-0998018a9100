package com.zto.intl.cts.hk.tds.adapter.base.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.zto.intl.cts.hk.base.facade.api.web.DubboActionMachineService;
import com.zto.intl.cts.hk.base.facade.api.web.dto.request.DubboActionMachineReqDTO;
import com.zto.intl.cts.hk.base.facade.api.web.dto.response.DubboActionMachineRespDTO;
import com.zto.intl.cts.hk.commons.util.BeanUtil;
import com.zto.intl.cts.hk.tds.adapter.base.ActionMachineService;
import com.zto.intl.cts.hk.tds.adapter.base.dto.request.ActionMachineValidDTO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 状态机校验
 * @Date 2025/8/4
 * @Version 1.0
 */
@Service
public class ActionMachineServiceImpl implements ActionMachineService {

    @Reference
    private DubboActionMachineService actionMachineService;


    public DubboActionMachineRespDTO validSingle(String actionCode, String orderStatus){
        return actionMachineService.validSingle(actionCode, orderStatus);
    }


    public DubboActionMachineRespDTO validBatch(String actionCode, List<ActionMachineValidDTO> orders){

        List<DubboActionMachineReqDTO> reqDTOS = BeanUtil.mapAsList(orders, DubboActionMachineReqDTO.class);
        return actionMachineService.validBatch(actionCode, reqDTOS);
    }
}
