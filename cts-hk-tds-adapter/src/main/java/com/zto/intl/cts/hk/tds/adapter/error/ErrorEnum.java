package com.zto.intl.cts.hk.tds.adapter.error;

import com.zto.intl.cts.hk.core.exception.IError;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @date 2019年6月25日 下午5:33:49
 * <AUTHOR>
 * @version 0.0.1
 */
public class ErrorEnum {

    /**
     * oms错误码
     * 2000-2100
     */
    @Getter
    @AllArgsConstructor
    public enum OmsError implements IError {

        DATA_QUERY_ERROR(2000, "数据查询异常"),
        OMS_LABEL_GET_ERROR(2001, "OMS获取面单异常"),
        ;

        private final Integer code;
        private final String message;

    }

    /**
     * 轨迹错误码
     * 2101 ~ 2200
     */
    @Getter
    @AllArgsConstructor
    public enum TrackError implements IError {

        PUSH_FAIL(2101, "推送轨迹失败"),
        ;
        private final Integer code;
        private final String message;
    }
}
