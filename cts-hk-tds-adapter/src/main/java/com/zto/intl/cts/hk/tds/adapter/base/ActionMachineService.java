package com.zto.intl.cts.hk.tds.adapter.base;

import com.zto.intl.cts.hk.base.facade.api.web.dto.response.DubboActionMachineRespDTO;
import com.zto.intl.cts.hk.tds.adapter.base.dto.request.ActionMachineValidDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 状态机校验
 * @Date 2025/8/4
 * @Version 1.0
 */
public interface ActionMachineService {


    DubboActionMachineRespDTO validSingle(String actionCode, String orderStatus);


    DubboActionMachineRespDTO validBatch(String actionCode, List<ActionMachineValidDTO> orders);
}
