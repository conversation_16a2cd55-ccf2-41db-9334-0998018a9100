package com.zto.intl.cts.hk.base.provider.impl.web.traceAction;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.dubbo.config.annotation.Service;
import com.zto.intl.cts.hk.base.dao.entity.TraceAction;
import com.zto.intl.cts.hk.base.dao.mapper.TraceActionMapper;
import com.zto.intl.cts.hk.base.facade.api.web.DubboTraceActionService;
import com.zto.intl.cts.hk.base.facade.api.web.dto.response.DubboTraceActionRespDTO;
import com.zto.intl.cts.hk.commons.constant.SymbolConstant;
import com.zto.intl.cts.hk.commons.util.BeanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 操作状态和轨迹映射
 * @Date 2025/8/2
 * @Version 1.0
 */
@Service
@org.springframework.stereotype.Service
public class DubboTraceActionServiceImpl implements DubboTraceActionService {

    @Autowired
    TraceActionMapper traceActionMapper;

    @Cacheable
    @Override
    public DubboTraceActionRespDTO getByActionCode(String actionCode){

        if(StringUtil.isBlank(actionCode)){
            return null;
        }
        Example example = new Example(TraceAction.class);
        Example.Criteria criteria = example.createCriteria();

        criteria.andEqualTo(TraceAction.ACTION_CODE, actionCode);
        TraceAction traceAction = traceActionMapper.selectOneByExample(example);

        return BeanUtil.copyProperties(traceAction, new DubboTraceActionRespDTO());
    }

    @Override
    public List<DubboTraceActionRespDTO> likeByCodeOrName(String keyword){

        List<TraceAction> traceActions;
        if(StringUtil.isBlank(keyword)){
            traceActions = traceActionMapper.selectAll();
        }else {

            Example example = new Example(TraceAction.class);
            Example.Criteria criteria = example.createCriteria();

            String keywordLike = SymbolConstant.PERCENT + keyword + SymbolConstant.PERCENT;
            criteria.andLike(TraceAction.ACTION_CODE, keywordLike);
            criteria.orLike(TraceAction.ACTION_NAME, keywordLike);
            traceActions = traceActionMapper.selectByExample(example);
        }

        return BeanUtil.mapAsList(traceActions, DubboTraceActionRespDTO.class);
    }
}
