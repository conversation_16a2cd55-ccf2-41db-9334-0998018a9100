package com.zto.intl.cts.hk.base.provider;

import com.zto.intl.cts.hk.commons.util.SpringbootStartInit;
import com.zto.titans.common.annotation.EnableFramework;
import com.zto.titans.orm.annotation.EnableMyBatis;
import com.zto.titans.soa.dubbo.annotation.EnableDubbo;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import tk.mybatis.spring.annotation.MapperScan;

/**
 * <AUTHOR>
 * @Description 通用域启动类
 * @Date 2025/7/19
 * @Version 1.0
 */
@EnableFramework
@ComponentScan(value = "com.zto.intl.cts.hk")
@MapperScan("com.zto.intl.cts.hk.tds.dao.mapper")
@EnableDubbo
@EnableCaching
@EnableMyBatis
public class CtsHkBaseApplication extends SpringbootStartInit {

    public static void main(String[] args) {
        init();
        new SpringApplicationBuilder(CtsHkBaseApplication.class)
                .web(WebApplicationType.NONE)
                .run(args);
    }

}
