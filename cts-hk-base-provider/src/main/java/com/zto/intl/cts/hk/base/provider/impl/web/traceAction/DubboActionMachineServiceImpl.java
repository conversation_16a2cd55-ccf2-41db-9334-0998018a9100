package com.zto.intl.cts.hk.base.provider.impl.web.traceAction;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.dubbo.config.annotation.Service;
import com.zto.intl.cts.hk.base.facade.api.web.DubboActionMachineService;
import com.zto.intl.cts.hk.base.facade.api.web.DubboTraceActionService;
import com.zto.intl.cts.hk.base.facade.api.web.dto.request.DubboActionMachineReqDTO;
import com.zto.intl.cts.hk.base.facade.api.web.dto.response.DubboActionMachineRespDTO;
import com.zto.intl.cts.hk.base.facade.api.web.dto.response.DubboTraceActionRespDTO;
import com.zto.intl.cts.hk.commons.constant.SymbolConstant;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * <AUTHOR>
 * @Description 状态机器
 * @Date 2025/8/2
 * @Version 1.0
 */
@Service
public class DubboActionMachineServiceImpl implements DubboActionMachineService {

    @Autowired
    DubboTraceActionService dubboTraceActionService;

    public DubboActionMachineRespDTO validSingle(String actionCode, String orderStatus){

        String errorMsg = buildErrorMsg(actionCode);
        if(StringUtil.isBlank(errorMsg)){
            return DubboActionMachineRespDTO.success();
        }

        return doValid(actionCode, orderStatus) ? DubboActionMachineRespDTO.success() : DubboActionMachineRespDTO.fail(errorMsg);
    }

    public DubboActionMachineRespDTO validBatch(String actionCode, List<DubboActionMachineReqDTO> orders){

        String errorMsg = buildErrorMsg(actionCode);
        if(StringUtil.isBlank(errorMsg)){
            return null;
        }

        List<String> errorBillNos = new ArrayList<>();
        orders.forEach(e ->{

            if(doValid(actionCode, e.getOrderStatus())){
                errorBillNos.add(e.getBizCode());
            }
        });

        return DubboActionMachineRespDTO.fail(errorMsg, errorBillNos);
    }


    /**
     * 错误描述
     * <AUTHOR>
     * @Date 2025/8/2
     */
    private String buildErrorMsg(String actionCode) {

        // 获取状态
        DubboTraceActionRespDTO action = dubboTraceActionService.getByActionCode("");

        // 订单状态代码转为枚举
        if (StringUtil.isNotBlank(action.getWhiteList())) {
            return actionCode + "仅允许" + action.getWhiteList();
        }

        if (StringUtil.isNotBlank(action.getBlackList())) {
            return actionCode + "不允许" + action.getBlackList();
        }

        return null;
    }


    /**
     * 校验
     * <AUTHOR>
     * @Date 2025/8/2
     */
    private Boolean doValid(String actionCode, String orderStatus){

        // 获取状态
        DubboTraceActionRespDTO action = dubboTraceActionService.getByActionCode("");

        // 订单状态代码转为枚举
        if(StringUtil.isNotBlank(action.getWhiteList())){
            return Arrays.asList(action.getWhiteList().split(SymbolConstant.COMMA)).contains(orderStatus);
        }

        if(StringUtil.isNotBlank(action.getBlackList())){
            return ! Arrays.asList(action.getBlackList().split(SymbolConstant.COMMA)).contains(orderStatus);
        }

        return null;
    }


}
