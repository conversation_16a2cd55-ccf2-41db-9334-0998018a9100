package com.zto.intl.cts.hk.base.provider.impl.external;

import com.alibaba.dubbo.config.annotation.Service;
import com.zto.intl.cts.hk.base.facade.api.external.PayDubboService;
import com.zto.intl.cts.hk.base.provider.enums.ErrorEnum;
import com.zto.intl.cts.hk.core.domain.external.BaseResponse;
import com.zto.intl.cts.hk.core.exception.BizException;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/7/19
 * @Version 1.0
 */
@Service
public class PayDubboServiceImpl implements PayDubboService {


    @Override
    public BaseResponse<String> testResponse() {

        if(true) {
            throw new BizException(ErrorEnum.UserError.TEST_ERROR);
        }

        return BaseResponse.success("order");
    }

}
