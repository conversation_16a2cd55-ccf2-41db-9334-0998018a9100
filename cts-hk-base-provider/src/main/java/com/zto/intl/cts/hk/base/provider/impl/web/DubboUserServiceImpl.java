package com.zto.intl.cts.hk.base.provider.impl.web;

import com.alibaba.dubbo.config.annotation.Service;
import com.zto.intl.cts.hk.base.facade.api.web.DubboUserService;
import com.zto.intl.cts.hk.base.provider.enums.ErrorEnum;
import com.zto.intl.cts.hk.base.provider.impl.web.user.response.UserInfoTest;
import com.zto.intl.cts.hk.commons.util.Asserts;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/7/19
 * @Version 1.0
 */
@Service
public class DubboUserServiceImpl implements DubboUserService {


    @Override
    public String testWeb() {
        Asserts.isTrue(true, ErrorEnum.UserError.TEST_ERROR, new UserInfoTest(10,2));
        return null;
    }
}
