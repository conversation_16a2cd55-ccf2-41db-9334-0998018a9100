/**
 * @date 2019年6月25日 下午5:33:49
 * <AUTHOR>
 * @version 0.0.1
 */
package com.zto.intl.cts.hk.base.provider.enums;


import com.zto.intl.cts.hk.core.exception.IError;
import lombok.AllArgsConstructor;
import lombok.Getter;

public class ErrorEnum {

    /**
     * 通用错误码
     * 500 ~ 1000
     */
    @Getter
    @AllArgsConstructor
    public enum CommonError implements IError {
        // 系统错误
        SYSTEM_ERROR(500, "system error"),
        // 配置错误
        CONFIG_ERROR(501, "config error"),
        // 参数错误
        PARAMETER_ERROR(502, "parameter error"),
        // 依赖服务异常
        DEPENDENT_API_INVOKE_FAIL(503, "dependent api invoke fail"),
        // 记录不存在
        RECORD_NOT_EXISTS(504, "记录不存在"),
        // 记录已存在
        RECORD_ALREADY_EXISTED(505, "record alread existed"),
        // 数据库更新失败
        DB_UPDATE_FAIL(506, "db record update fail"),
        // 数据库插入失败
        DB_INSERT_FAIL(507, "insert into db fail"),
        // 数据库删除失败
        DB_DELETE_FAIL(508, "delete from db fail"),
        //没有权限
        HAVE_NO_AUTH(509,"auth check error"),
        //线程池拒绝
        THREAD_POOL_REJECT(510,"thread pool reject"),
        //es查询失败
        ES_SEARCH_FAIL(511,"es search fail"),
        //基础资料服务调用失败
        BASE_SERVIC_INVOKE_FAIL(512,"base service invoke fail"),
        //json转化失败
        JSON_PARSE_FAIL(513,"message parse fail"),
        // 并发导致异常
        CONCURRENT_ERROR(514,"concurrent error"),
        // 批量处理超限
        BATCH_SIZE_LIMITED(515, "batch size limited"),
        // 数据库操作失败
        DB_OPERATE_FAIL(516, "db operate fail"),
        //批量操作失败
        BTACH_OPERATE_FAIL(517,"batch operate fail"),
        //redis操作失敗
        REDIS_OPERATE_FAIL(518,"redis operate fail"),
        GET_LOCK_EXCEPTION(520,"获取锁异常"),
        SEND_MSG_FAILED(521, "发送消息失败"),
        EXPORT_EXCEL_UPLOAD_FAIL(522,"导出excel上传失败"),
        EXPORT_EXCEL_SIGN_EXCEPTION(523,"导出excel签名异常"),
        GET_URL_FAIL(524,"获取文件下载地址失败"),
        PARAMETER_IS_EMPTY(525,"参数为空"),
        START_AFTER_END(526,"开始日期大于结束日期"),
        DATE_OVER_ONE_MONTH(527,"日期相差超过30天"),
        TRY_LATER(528, "稍后重试"),
        GET_OWNER_SITE_INFO_NULL(529,"根据所属站点获取信息为空")

        ;

        private Integer code;
        private String message;

    }


    /**
     * 到货扫描模块错误码
     * 1000 ~ 1100
     */
    @Getter
    @AllArgsConstructor
    public enum UserError implements IError {
        TEST_ERROR(1001,"非法用户哦");
        private Integer code;
        private String message;
    }
}
