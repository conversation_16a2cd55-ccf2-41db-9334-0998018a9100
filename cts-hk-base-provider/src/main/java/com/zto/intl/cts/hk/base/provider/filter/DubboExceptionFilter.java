package com.zto.intl.cts.hk.base.provider.filter;

import com.alibaba.dubbo.common.Constants;
import com.alibaba.dubbo.common.extension.Activate;
import com.alibaba.dubbo.rpc.*;
import com.zto.intl.cts.hk.base.provider.enums.ErrorEnum;
import com.zto.intl.cts.hk.core.domain.external.BaseResponse;
import com.zto.intl.cts.hk.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;

import javax.validation.ConstraintViolationException;
import java.lang.reflect.Method;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Description dubbo异常过滤器
 * @Date 2025/7/25
 * @Version 1.0
 */
@Activate(group = Constants.PROVIDER)
@Slf4j
public class DubboExceptionFilter implements Filter {

    // 使用ConcurrentHashMap缓存Method对象
//    private static final ConcurrentHashMap<MethodKey, Method> METHOD_CACHE = new ConcurrentHashMap<>();

    // 自定义缓存Key
//    record MethodKey(Class<?> clazz, String methodName, Class<?>[] paramTypes) {}

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {

        Result result = null;
        try {
            result = invoker.invoke(invocation);
            if (result.hasException()) {
                return handleException(invocation, result.getException());

            }
            return result;

        }catch (Throwable e){
            return handleException(invocation, result.getException());
        }

    }

    private Result handleException(Invocation invocation, Throwable e) {

        Class<?> interfaceClass  = invocation.getInvoker().getInterface();
        log.error("Dubbo服务异常, 接口: {}, 方法: {}",
                interfaceClass.getSimpleName(),
                invocation.getMethodName(),
                e);


        // TODO 性能开销优化
        Method method = null;
        try {
            // 通过反射获取Method对象
            method = interfaceClass.getMethod(invocation.getMethodName(), invocation.getParameterTypes());
        } catch (Exception ex) {
            return new RpcResult(ex);
        }

        // 返回方法的声明返回类型
        Class<?> returnType = method.getReturnType();
        // 非DubboResponse类型
        if (!BaseResponse.class.isAssignableFrom(returnType)) {
            return new RpcResult(e);
        }

        // 业务异常直接返回
        if (e instanceof BizException) {
            BizException be = (BizException) e;
            return new RpcResult(BaseResponse.fail(be.getCode(), be.getMessage()));
        }

        // 2. 参数校验异常
        if (e instanceof ConstraintViolationException) {
            ConstraintViolationException cve = (ConstraintViolationException) e;
            String message = cve.getConstraintViolations().stream()
                    .map(cv -> cv.getPropertyPath() + ": " + cv.getMessage())
                    .collect(Collectors.joining("; "));
            return new RpcResult(BaseResponse.fail(ErrorEnum.CommonError.PARAMETER_ERROR.getCode(), message));
        }

        // 3. 系统异常记录日志并封装
        return new RpcResult(BaseResponse.fail(ErrorEnum.CommonError.SYSTEM_ERROR.getCode(), ErrorEnum.CommonError.SYSTEM_ERROR.getMessage()));
    }


}
