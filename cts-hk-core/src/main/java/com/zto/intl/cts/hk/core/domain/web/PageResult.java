package com.zto.intl.cts.hk.core.domain.web;

import com.github.pagehelper.Page;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分页结果封装类
 *
 * @param <T> 数据类型
 * <AUTHOR>
 * @date 2025/7/24
 */
public class PageResult<T> {

    /** 当前页码 */
    private Integer pageNum;

    /** 每页大小 */
    private Integer pageSize;

    /** 总页数 */
    private Integer pages;

    /** 总记录数 */
    private Long total;

    /** 数据列表 */
    private List<T> data;

    /**
     * 默认构造函数
     */
    public PageResult() {
        this.data = new ArrayList<>();
        this.total = 0L;
        this.pageNum = 1;
        this.pageSize = 10;
        this.pages = 0;
    }

    /**
     * 全参构造函数
     */
    public PageResult(Integer pageNum, Integer pageSize, Integer pages, Long total, List<T> data) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.pages = pages;
        this.total = total;
        this.data = data != null ? data : new ArrayList<>();
    }

    /**
     * 简化构造函数
     */
    public PageResult(List<T> data, Long total) {
        this();
        this.data = data != null ? data : new ArrayList<>();
        this.total = total != null ? total : 0L;
    }

    /**
     * 从Page对象创建PageResult
     */
    public static <T> PageResult<T> map(Page<?> page, List<T> data) {
        return new PageResult<>(page.getPageNum(), page.getPageSize(), page.getPages(), page.getTotal(), data);
    }

    // Getter and Setter methods
    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPages() {
        return pages;
    }

    public void setPages(Integer pages) {
        this.pages = pages;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }

    // 静态工厂方法

    /**
     * 创建空的分页结果
     */
    public static <T, P> PageResult<P> empty(PageQuery<T> pageQuery) {
        return new PageResult<>(pageQuery.getPageNum(), pageQuery.getPageSize(), 0, 0L, new ArrayList<>());
    }

    /**
     * 创建空的分页结果
     */
    public static <P> PageResult<P> empty(Integer pageNum, Integer pageSize) {
        return new PageResult<>(pageNum, pageSize, 0, 0L, new ArrayList<>());
    }

    /**
     * 从Page对象和转换函数创建PageResult
     */
    public <E> PageResult(Page<E> source, Function<E, T> function) {
        this.pageNum = source.getPageNum();
        this.pageSize = source.getPageSize();
        this.pages = source.getPages();
        this.total = source.getTotal();
        this.data = source.stream().map(function).collect(Collectors.toList());
    }

    // 转换方法

    /**
     * 转换PageResult内部数据，T -> R
     */
    public <R> PageResult<R> convert(Function<T, R> function) {
        PageResult<R> pageResult = new PageResult<>();
        pageResult.pageNum = this.pageNum;
        pageResult.pageSize = this.pageSize;
        pageResult.pages = this.pages;
        pageResult.total = this.total;
        pageResult.data = this.data != null ?
            this.data.stream().map(function).collect(Collectors.toList()) :
            new ArrayList<>();
        return pageResult;
    }

    /**
     * 转换PageResult内部数据，直接设置新的数据列表
     */
    public <R> PageResult<R> convert(List<R> dataList) {
        PageResult<R> pageResult = new PageResult<>();
        pageResult.pageNum = this.pageNum;
        pageResult.pageSize = this.pageSize;
        pageResult.pages = this.pages;
        pageResult.total = this.total;
        pageResult.data = dataList != null ? dataList : new ArrayList<>();
        return pageResult;
    }

    // 工具方法

    /**
     * 判断是否为空结果
     */
    public boolean isEmpty() {
        return data == null || data.isEmpty();
    }

    /**
     * 判断是否有数据
     */
    public boolean hasData() {
        return !isEmpty();
    }

    /**
     * 获取数据大小
     */
    public int size() {
        return data != null ? data.size() : 0;
    }

    /**
     * 是否为第一页
     */
    public boolean isFirstPage() {
        return pageNum != null && pageNum <= 1;
    }

    /**
     * 是否为最后一页
     */
    public boolean isLastPage() {
        return pageNum != null && pages != null && pageNum >= pages;
    }

    /**
     * 是否有下一页
     */
    public boolean hasNextPage() {
        return !isLastPage();
    }

    /**
     * 是否有上一页
     */
    public boolean hasPreviousPage() {
        return !isFirstPage();
    }

    /**
     * 计算总页数
     */
    public void calculatePages() {
        if (pageSize != null && pageSize > 0 && total != null) {
            this.pages = (int) Math.ceil((double) total / pageSize);
        }
    }

    @Override
    public String toString() {
        return "PageResult{" +
                "pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                ", pages=" + pages +
                ", total=" + total +
                ", size=" + size() +
                '}';
    }
}
