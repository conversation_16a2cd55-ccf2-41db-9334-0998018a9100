package com.zto.intl.cts.hk.core.exception;


import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description dubbo自定义异常
 * @Date 2025/7/25
 * @Version 1.0
 */

public class  BizException extends RuntimeException implements Serializable {

    private static final long serialVersionUID = -7739180317485624414L;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    private int code;
    private Object data;

    public BizException(IError error){
        super(error.getMessage());
        this.code = error.getCode();
    }


    public BizException(IError error, Object data){
        super(error.getMessage());
        this.code = error.getCode();
        this.data = data;
    }

    public BizException(int code, String message){
        super(message);
        this.code = code;
    }

}
