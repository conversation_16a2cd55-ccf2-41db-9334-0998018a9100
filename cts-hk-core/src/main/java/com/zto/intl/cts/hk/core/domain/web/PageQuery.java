package com.zto.intl.cts.hk.core.domain.web;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import java.io.Serializable;
import java.util.function.Function;

/**
 * 分页查询请求封装类
 *
 * @param <T> 查询条件类型
 * <AUTHOR>
 * @date 2025/7/24
 */
public class PageQuery<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 默认页码 */
    private static final Integer DEFAULT_PAGE_NUM = 1;

    /** 默认每页大小 */
    private static final Integer DEFAULT_PAGE_SIZE = 100;

    /**
     * 页数
     */
    private Integer pageNum = DEFAULT_PAGE_NUM;

    /**
     * 每页查询数量
     */
    private Integer pageSize = DEFAULT_PAGE_SIZE;

    /**
     * 排序条件
     */
    private String orderBy;

    /**
     * 查询条件
     */
    private T condition;

    // 构造函数

    /**
     * 默认构造函数
     */
    public PageQuery() {
    }

    /**
     * 全参构造函数
     */
    public PageQuery(Integer pageNum, Integer pageSize, String orderBy, T condition) {
        this.pageNum = pageNum != null ? pageNum : DEFAULT_PAGE_NUM;
        this.pageSize = pageSize != null ? pageSize : DEFAULT_PAGE_SIZE;
        this.orderBy = orderBy;
        this.condition = condition;
    }

    /**
     * 简化构造函数
     */
    public PageQuery(Integer pageNum, Integer pageSize, T condition) {
        this(pageNum, pageSize, null, condition);
    }

    /**
     * 只设置查询条件的构造函数
     */
    public PageQuery(T condition) {
        this(DEFAULT_PAGE_NUM, DEFAULT_PAGE_SIZE, null, condition);
    }

    // Getter and Setter methods

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum != null ? pageNum : DEFAULT_PAGE_NUM;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize != null ? pageSize : DEFAULT_PAGE_SIZE;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public T getCondition() {
        return condition;
    }

    public void setCondition(T condition) {
        this.condition = condition;
    }

    // 工具方法

    /**
     * 使用PageHelper分页
     */
    public <E> Page<E> startPage() {
        return PageHelper.startPage(pageNum, pageSize);
    }

    /**
     * 转换查询条件类型 PageQuery<A> -> PageQuery<B>
     *
     * @param mapper 转换函数
     * @param <E> 目标查询条件类型
     * @return 转换后的PageQuery
     */
    public <E> PageQuery<E> convert(Function<T, E> mapper) {
        PageQuery<E> newPageQuery = new PageQuery<>();
        newPageQuery.setPageNum(this.pageNum);
        newPageQuery.setPageSize(this.pageSize);
        newPageQuery.setOrderBy(this.orderBy);
        newPageQuery.setCondition(this.condition != null ? mapper.apply(this.condition) : null);
        return newPageQuery;
    }

    /**
     * 转换查询条件类型，直接设置新的查询条件
     *
     * @param newCondition 新的查询条件
     * @param <E> 目标查询条件类型
     * @return 转换后的PageQuery
     */
    public <E> PageQuery<E> convert(E newCondition) {
        PageQuery<E> newPageQuery = new PageQuery<>();
        newPageQuery.setPageNum(this.pageNum);
        newPageQuery.setPageSize(this.pageSize);
        newPageQuery.setOrderBy(this.orderBy);
        newPageQuery.setCondition(newCondition);
        return newPageQuery;
    }

    /**
     * 兼容旧版本的createPageQuery方法
     * @deprecated 请使用convert()方法
     */
    @Deprecated
    public <E> PageQuery<E> createPageQuery(Function<T, E> fun) {
        return convert(fun);
    }

    // 静态工厂方法

    /**
     * 创建只包含查询条件的PageQuery
     */
    public static <T> PageQuery<T> of(T condition) {
        return new PageQuery<>(condition);
    }

    /**
     * 创建包含分页信息和查询条件的PageQuery
     */
    public static <T> PageQuery<T> of(Integer pageNum, Integer pageSize, T condition) {
        return new PageQuery<>(pageNum, pageSize, condition);
    }

    /**
     * 创建包含完整信息的PageQuery
     */
    public static <T> PageQuery<T> of(Integer pageNum, Integer pageSize, String orderBy, T condition) {
        return new PageQuery<>(pageNum, pageSize, orderBy, condition);
    }

    // 工具方法

    /**
     * 判断是否为第一页
     */
    public boolean isFirstPage() {
        return pageNum != null && pageNum <= 1;
    }

    /**
     * 获取偏移量
     */
    public int getOffset() {
        if (pageNum == null || pageSize == null) {
            return 0;
        }
        return (pageNum - 1) * pageSize;
    }

    /**
     * 验证分页参数是否有效
     */
    public boolean isValidPage() {
        return pageNum != null && pageNum > 0 && pageSize != null && pageSize > 0;
    }

    @Override
    public String toString() {
        return "PageQuery{" +
                "pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                ", orderBy='" + orderBy + '\'' +
                ", condition=" + condition +
                '}';
    }
}
