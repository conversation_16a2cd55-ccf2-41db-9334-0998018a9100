package com.zto.intl.cts.hk.core.domain.external;

import com.zto.intl.cts.hk.core.exception.IError;

import java.io.Serializable;

/**
 * 对外Dubbo服务统一响应封装类
 *
 * @param <T> 响应数据类型
 * <AUTHOR>
 * @version 2.0
 * @date 2025/7/25
 */
public class BaseResponse<T> implements Serializable {
    private static final long serialVersionUID = -1059825926335653835L;

    private T data;
    private Boolean success;
    private BaseError error;

    /**
     * 默认构造函数
     */
    public BaseResponse() {
        // 默认构造函数
    }

    /**
     * 成功响应构造函数
     *
     * @param data 响应数据
     */
    public BaseResponse(T data) {
        this.data = data;
        this.success = true;
    }

    /**
     * 创建成功响应（无数据）
     *
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> BaseResponse<T> success() {
        BaseResponse<T> baseResponse = new BaseResponse<>();
        baseResponse.setSuccess(true);
        return baseResponse;
    }

    /**
     * 创建成功响应（带数据）
     *
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> BaseResponse<T> success(T data) {
        return new BaseResponse<>(data);
    }

    /**
     * 创建失败响应（无数据）
     *
     * @param errorCode 错误码
     * @param errorMsg 错误信息
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> BaseResponse<T> fail(int errorCode, String errorMsg) {
        BaseResponse<T> baseResponse = new BaseResponse<>();
        baseResponse.error = new BaseError(errorCode, errorMsg);
        baseResponse.success = false;
        return baseResponse;
    }

    /**
     * 创建失败响应（带数据）
     *
     * @param errorCode 错误码
     * @param errorMsg 错误信息
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> BaseResponse<T> fail(int errorCode, String errorMsg, T data) {
        BaseResponse<T> baseResponse = new BaseResponse<>();
        baseResponse.error = new BaseError(errorCode, errorMsg);
        baseResponse.data = data;
        baseResponse.success = false;
        return baseResponse;
    }

    /**
     * 创建失败响应（使用IError接口）
     *
     * @param error 错误信息接口
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> BaseResponse<T> fail(IError error) {
        return fail(error.getCode(), error.getMessage());
    }

    /**
     * 创建失败响应（使用IError接口，带数据）
     *
     * @param error 错误信息接口
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> BaseResponse<T> fail(IError error, T data) {
        return fail(error.getCode(), error.getMessage(), data);
    }

    // ==================== Getter and Setter ====================

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public BaseError getError() {
        return error;
    }

    public void setError(BaseError error) {
        this.error = error;
    }

    // ====================  ====================

    /**
     * 判断响应是否成功
     *
     * @return true-成功，false-失败
     */
    public boolean isSuccess() {
        return Boolean.TRUE.equals(success);
    }

    /**
     * 判断响应是否失败
     *
     * @return true-失败，false-成功
     */
    public boolean isFailed() {
        return !isSuccess();
    }

    /**
     * 获取错误码
     *
     * @return 错误码，成功时返回0
     */
    public int getErrorCode() {
        return error != null ? error.getCode() : 0;
    }

    /**
     * 获取错误信息
     *
     * @return 错误信息，成功时返回null
     */
    public String getErrorMessage() {
        return error != null ? error.getMessage() : null;
    }


    /**
     * 基础错误信息类
     */
    public static class BaseError implements Serializable {
        private static final long serialVersionUID = 1196345933197153607L;

        /** 错误码 */
        private int code;

        /** 错误信息 */
        private String message;

        /**
         * 默认构造函数
         */
        public BaseError() {
            // 默认构造函数
        }

        /**
         * 构造函数
         *
         * @param code 错误码
         * @param message 错误信息
         */
        public BaseError(int code, String message) {
            this.code = code;
            this.message = message;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        @Override
        public String toString() {
            return "BaseError{" +
                    "code=" + code +
                    ", message='" + message + '\'' +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "BaseResponse{" +
                "data=" + data +
                ", success=" + success +
                ", error=" + error +
                '}';
    }
}
