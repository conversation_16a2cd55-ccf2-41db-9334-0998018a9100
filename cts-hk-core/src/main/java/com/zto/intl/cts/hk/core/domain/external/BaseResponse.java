package com.zto.intl.cts.hk.core.domain.external;


import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description dubbo服务响应
 * @Date 2025/7/25
 * @Version 1.0
 */
public class BaseResponse<T> implements Serializable {
    private static final long serialVersionUID = -1059825926335653835L;

    private T data;
    private Boolean success;
    private BaseError error;

    public BaseResponse(){
    }

    public BaseResponse(T data){
        this.data = data;
        this.success = true;
    }

    public static  <T> BaseResponse success(T data){
        BaseResponse baseResponse = new BaseResponse(data);
        return baseResponse;
    }


    public static BaseResponse fail(int errorCode, String errorMsg){
        BaseResponse baseResponse = new BaseResponse();
        BaseError dubboError = new BaseError();
        dubboError.message = errorMsg;
        baseResponse.error = dubboError;
        return baseResponse;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public BaseError getError() {
        return error;
    }

    public void setError(BaseError error) {
        this.error = error;
    }


    public static class BaseError implements Serializable{
        private static final long serialVersionUID = 1196345933197153607L;
        // 错误CODE
        private int code;
        // 错误信息
        private String message;

        public BaseError() {
        }

        public BaseError(int code, String message) {
            this.code = code;
            this.message = message;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }
}
