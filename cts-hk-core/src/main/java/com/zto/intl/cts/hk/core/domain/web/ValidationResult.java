package com.zto.intl.cts.hk.core.domain.web;

/**
 * 校验结果封装类
 * */
public class ValidationResult {
    /**
     * 是否校验成功
     */
    private boolean valid;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 警告信息（校验成功但有提示）
     */
    private String warningMessage;

    public ValidationResult() {
    }


    public ValidationResult(boolean valid, String errorMessage) {
        this.valid = valid;
        this.errorMessage = errorMessage;
    }

    public ValidationResult(boolean valid, String errorMessage, String warningMessage) {
        this.valid = valid;
        this.errorMessage = errorMessage;
        this.warningMessage = warningMessage;
    }

    /**
     * 创建成功结果
     */
    public static ValidationResult success() {
        return new ValidationResult(true, null);
    }

    /**
     * 创建成功结果（带警告）
     */
    public static ValidationResult successWithWarning(String warningMessage) {
        return new ValidationResult(true, null, warningMessage);
    }

    /**
     * 创建失败结果
     */
    public static ValidationResult failure(String errorMessage) {
        return new ValidationResult(false, errorMessage);
    }

    public boolean isValid() {
        return valid;
    }

    public void setValid(boolean valid) {
        this.valid = valid;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getWarningMessage() {
        return warningMessage;
    }

    public void setWarningMessage(String warningMessage) {
        this.warningMessage = warningMessage;
    }

    /**
     * 是否有警告信息
     */
    public boolean hasWarning() {
        return warningMessage != null && !warningMessage.trim().isEmpty();
    }
}
